<!DOCTYPE html>
<html lang="fr" class="h-full">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Droits du Locataire en France : Guide Complet et Législation (Bail, Expulsion, Dépôt de Garantie)</title>
    <meta name="description"
        content="Découvrez vos droits fondamentaux en tant que locataire en France. Ce guide complet couvre la législation sur la location, le bail, la protection contre l'expulsion, la restitution du dépôt de garantie, la sous-location, les animaux domestiques, les travaux, et les recours en cas de litige. Informez-vous sur QuestionLegale.info.">
    <meta name="author" content="QuestionLegale.info">
    <meta name="keywords"
        content="droits locataire France, législation location, bail habitation, expulsion locataire, dépôt de garantie, sous-location, animaux logement, RGPD locataire, nuisances voisinage, vente logement locataire, succession locataire, travaux logement, sinistre locataire, discrimination location, droit immobilier, QuestionLegale.info">

    <meta property="og:title" content="Droits du Locataire en France : Guide Complet et Législation (Bail, Expulsion, Dépôt de Garantie)">
    <meta property="og:description"
        content="Découvrez vos droits fondamentaux en tant que locataire en France. Ce guide complet couvre la législation sur la location, le bail, la protection contre l'expulsion, la restitution du dépôt de garantie, la sous-location, les animaux domestiques, les travaux, et les recours en cas de litige. Informez-vous sur QuestionLegale.info.">
    <meta property="og:image" content="../img/logo.png">
    <meta property="og:url" content="https://www.questionlegale.info/Articles/Vos-Droits-en-Tant-que-Locataire.htm">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="fr_FR">
    <meta property="og:site_name" content="QuestionLegale.info">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Local JS (detecteur francais, localisation avocat naotaire) -->
    <script src="../js/main.js"></script>

    <!-- Basic Favicon -->
    <link rel="icon" href="../img/favicons/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../img/favicons/favicon.ico" type="image/x-icon">

    <!-- Modern Browsers -->
    <link rel="icon" type="image/png" sizes="32x32" href="../img/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../img/favicons/favicon-16x16.png">

    <!-- Apple Devices -->
    <link rel="apple-touch-icon" sizes="180x180" href="../img/favicons/apple-touch-icon.png">
    <link rel="mask-icon" href="../img/favicons/safari-pinned-tab.svg" color="#5bbad5">

    <!-- Android Chrome -->
    <link rel="manifest" href="../img/favicons/site.webmanifest">
    <meta name="theme-color" content="#ffffff">

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="../img/favicons/browserconfig.xml">
    <meta name="msapplication-TileImage" content="../img/favicons/mstile-144x144.png">

    <!-- SVG Favicon (Optional) -->
    <link rel="icon" type="image/svg+xml" href="../img/favicons/favicon.svg">

    <!-- Theme Colors for Different Platforms -->
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
    <meta name="theme-color" content="#000000" media="(prefers-color-scheme: dark)">
    <meta itemprop="image" content="../img/favicons/logo.png">


    <style>
        /* ========================================
   Enhanced Blog Content Styles
   ======================================== */

        /* Modern CSS Custom Properties for Consistent Theming */
        :root {
            --blog-primary-color: #1a1a2e;
            --blog-secondary-color: #4a5568;
            --blog-accent-color: #667eea;
            --blog-text-color: #2d3748;
            --blog-light-bg: #f8fafc;
            --blog-hover-bg: #edf2f7;
            --blog-border-color: #e2e8f0;
            --blog-spacing-xs: 0.5rem;
            --blog-spacing-sm: 1rem;
            --blog-spacing-md: 1.5rem;
            --blog-spacing-lg: 2rem;
            --blog-spacing-xl: 2.5rem;
            --blog-border-radius: 0.5rem;
            --blog-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Base Blog Content Container */
        .blog-content {
            max-width: 65ch;
            /* Optimal reading width */
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: var(--blog-text-color);
            line-height: 1.7;
        }

        /* Enhanced Image Styles */
        .blog-content img {
            max-width: 100%;
            height: auto;
            margin: var(--blog-spacing-xl) 0;
            border-radius: var(--blog-border-radius);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: var(--blog-transition);
        }

        .blog-content img:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        /* Improved Typography */
        .blog-content p {
            margin-bottom: var(--blog-spacing-md);
            line-height: 1.2;
            font-size: 1.1rem;
            font-weight: 400;
        }

        /* Modern Heading Styles */
        .blog-content h2 {
            font-size: clamp(1.75rem, 4vw, 2.5rem);
            /* Responsive sizing */
            font-weight: 800;
            margin: var(--blog-spacing-xl) 0 var(--blog-spacing-md);
            color: var(--blog-primary-color);
            letter-spacing: -0.025em;
            line-height: 1.2;
            position: relative;
            padding-bottom: var(--blog-spacing-sm);
        }

        /* Modern gradient underline instead of solid border */
        .blog-content h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, var(--blog-accent-color), transparent);
            border-radius: 2px;
            transition: var(--blog-transition);
        }

        .blog-content h2:hover::after {
            width: 100px;
        }

        .blog-content h3 {
            font-size: clamp(1.25rem, 3vw, 1.75rem);
            font-weight: 600;
            margin: var(--blog-spacing-lg) 0 0.75rem;
            color: var(--blog-secondary-color);
            letter-spacing: -0.01em;
            line-height: 1.1;
        }

        /* Clean and Professional List Styles */
        .blog-content ul {
            list-style: none;
            padding-left: 0;
            margin: var(--blog-spacing-md) 0;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .blog-content ul li {
            position: relative;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            background: #ffffff;
            font-size: 1.05rem;
            color: var(--blog-text-color);
            display: flex;
            align-items: flex-start;
            line-height: 1.1;
            font-weight: 400;
        }

        /* Professional checkmark */
        .blog-content ul li::before {
            content: '✓';
            position: absolute;
            left: 0.75rem;
            top: 0.9rem;
            width: 1rem;
            height: 1rem;
            background: var(--blog-accent-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.7rem;
            flex-shrink: 0;
        }

        /* Focus States for Accessibility */
        .blog-content ul li:focus-within {
            outline: 2px solid var(--blog-accent-color);
            outline-offset: 2px;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 768px) {
            :root {
                --blog-spacing-xl: 1.5rem;
                --blog-spacing-lg: 1.25rem;
                --blog-spacing-md: 1rem;
            }

            .blog-content {
                padding: 0 var(--blog-spacing-sm);
            }

            .blog-content p {
                font-size: 1rem;
                line-height: 1.7;
            }

            .blog-content ul {
                gap: 0.2rem;
            }

            .blog-content ul li {
                padding: 0.6rem 0.75rem 0.6rem 2.25rem;
                font-size: 1rem;
            }

            .blog-content ul li::before {
                left: 0.6rem;
                top: 0.75rem;
                width: 0.9rem;
                height: 0.9rem;
                font-size: 0.65rem;
            }
        }

        @media (max-width: 480px) {
            .blog-content ul li {
                padding: var(--blog-spacing-xs) var(--blog-spacing-xs) var(--blog-spacing-xs) 2.25rem;
                font-size: 0.95rem;
            }

            .blog-content ul li::before {
                left: var(--blog-spacing-xs);
            }
        }

        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: #3B82F6;
            z-index: 50;
            transition: width 0.2s ease;
        }

        .image-hover-zoom {
            overflow: hidden;
        }

        .image-hover-zoom img {
            transition: transform 0.5s ease;
        }

        .group:hover .image-hover-zoom img {
            transform: scale(1.1);
        }

        /* Placeholder image styles */
        .placeholder {
            position: relative;
            background: #f3f4f6;
            overflow: hidden;
        }

        .placeholder::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 50%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            100% {
                left: 100%;
            }
        }


        section {
            background-color: rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        section:hover {
            opacity: 1;
            transform: translateY(-5px);
        }

        section>div {
            /*background-color: rgba(255, 255, 255, 0.9);*/
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }

        section:hover>div {
            opacity: 1;
        }

        /* Position "signin" and "signup" buttons to the top right */

        /* Round the corners of buttons with icons */
        button i {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }

        button {
            border-radius: 20px;
            /* Adjust the radius as needed */
        }


        .textarea-zoom {
            transition: transform 0.3s ease;
        }

        .textarea-zoom:focus {
            transform: scale(1.05);
        }

        .text-5xl {
            font-size: 2.5rem;
            /* Default size */
        }


        .textarea-zoom:focus {
            outline: 2px solid #007bff;
            box-shadow: 0 0 5px #007bff;
        }

        @media (max-width: 768px) {
            .text-5xl {
                font-size: 2rem;
                /* Smaller size for smaller screens */
            }
        }

        @media (max-width: 768px) {
            #home {
                height: auto !important;
                min-height: 85vh;
            }

            #home .flex-col {
                padding-bottom: 1.5rem;
            }
        }
    </style>
</head>


<body class="bg-gray-200 text-gray-800 h-full">
    <div class="reading-progress" id="reading-progress"></div>

    <!-- Sticky Menu -->
    <nav
        class="fixed top-0 md:top-2 left-0 md:left-1/2 transform md:-translate-x-1/2 bg-white opacity-90 md:rounded-full shadow-xl px-4 md:px-6 py-1.5 flex items-center justify-between w-full md:w-[90%] md:max-w-5xl z-50">
        <!-- Left: Icon and Title -->
        <a href="../index.html" tooltip="Question Legale" ><div class="flex items-center space-x-2">
            
            <!-- Icon: replace emoji with SVG or image if needed -->
            <img src="../img/logos/main1.png" alt="Justice.fr" class="w-6 h-6">
            <div class="font-bold text-sm md:text-lg">QuestionLegale.Info</div>
        
    </div></a>

        <!-- Mobile Section: Burger and Buttons -->
        <div class="flex items-center md:hidden space-x-3">
            <!-- Buttons: visible on mobile -->
            <button id="mobileSignIn" type="button"
                class="px-2 py-0.5 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-xs whitespace-nowrap">
                Se Connecter
            </button>
            <button id="mobileSignUp" type="button"
                class="px-2 py-0.5 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-xs whitespace-nowrap">
                Inscription
            </button>
            <!-- Burger menu button -->
            <button id="burgerBtn" type="button" class="focus:outline-none p-0.5" aria-label="Toggle menu">
                <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>

        <!-- Center: Menu Items (Desktop) -->
        <div id="menuItems" class="hidden md:flex space-x-6 mx-auto w-full max-w-md justify-center">
            <a href="#analyse" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Question?</a>
            <a href="articleslist.html"
                class="text-gray-700 hover:text-blue-500 font-medium text-sm">Articles</a>
            <a href="#references" class="text-gray-700 hover:text-blue-500 font-medium text-sm">References</a>
        </div>

        <!-- Right: Sign In and Sign Up Buttons (Desktop) -->
        <div class="hidden md:flex items-center space-x-2">
            <button id="signInBtn" type="button"
                class="px-3 py-1 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-sm whitespace-nowrap">
                Se Connecter
            </button>
            <button id="signUpBtn" type="button"
                class="px-3 py-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-sm whitespace-nowrap">
                Inscription
            </button>
        </div>
    </nav>

    <!-- Mobile Menu: Only for menu items (dropdown) -->
    <div id="mobileMenu"
        class="fixed top-[45px] left-0 w-full bg-white shadow-md rounded-b-lg p-4 md:hidden hidden z-40">
        <div class="flex flex-col space-y-3">
            <a href="/#home" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Question</a>
            <a href="/#about" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">References</a>
            <a href="/#services" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Services</a>
            <a href="/#contact" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Contact</a>
            <a href="/#contact" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Contact</a>
        </div>
        <!-- Buttons in mobile menu -->
        <div class="flex space-x-3 mt-4">
            <button type="button"
                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-semibold rounded-full px-3 py-1 transition whitespace-nowrap">
                Se Connecter
            </button>
            <button type="button"
                class="flex-1 bg-red-600 hover:bg-red-700 text-white text-xs font-semibold rounded-full px-3 py-1 transition whitespace-nowrap">
                Inscription
            </button>
        </div>
    </div>

    <!-- Presentation -->
    <section id="home" class="relative mb-12 py-16 min-h-screen">
        <!-- Background image with overlay -->
        <div class="absolute inset-0 presentation-card bg-cover bg-right-top bg-no-repeat" data-aos="fade"
            data-aos-duration="1200">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>

        <div class="container mx-auto px-4 py-8 flex flex-col lg:flex-row gap-8">
            <!-- Main Content -->
            <main class="lg:w-3/4" data-aos="fade-up" data-aos-duration="1000">
                <article class="bg-white rounded-xl shadow-md p-6 lg:p-8">
                    <header class="mb-8">
                        <h1 class="text-4xl font-bold mb-4 text-gray-900">Vos Droits en Tant que Locataire</h1>
                        <div class="flex items-center text-gray-600 mb-4">
                            <div class="placeholder w-10 h-10 rounded-full mr-4">
                                <img src="../img/avocate2.png" alt="Auteur" class="w-10 h-10 rounded-full">
                            </div>
                            <div>
                                <p class="font-semibold">QuestionLegale.info</p>
                                <p class="text-sm">Publié le 29 mai 2025 · 10 min de lecture</p>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">#Locataire</span>
                            <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">#Droits</span>
                            <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">#Législation</span>
                        </div>
                    </header>

                    <div class="blog-content prose lg:prose-lg max-w-none">
                        <div class="placeholder rounded-xl mb-8 aspect-[2/1]">
                            <img src="../Articles/pict/Vos Droits en Tant que Locataire/Designer (1).jpeg" alt="Vos Droits en Tant que Locataire" class="rounded-xl w-full h-full object-cover svg-article-image">
                        </div>
                        <p>Cet article vise à clarifier les droits fondamentaux des locataires, en fournissant des informations essentielles sur la législation en matière de location. Que vous soyez un locataire expérimenté ou que vous envisagiez de louer pour la première fois, il est crucial de connaître vos droits afin de garantir un environnement de vie sûr et respectueux.</p>
                        <p>Les locataires en France bénéficient d'un ensemble de droits protégés par la loi, notamment la loi n°89-462 du 6 juillet 1989 relative aux droits des locataires et à la liberté locative, ainsi que par d'autres textes législatifs. Voici une présentation détaillée de ces droits, organisée en catégories pour une meilleure compréhension.</p>

                        <h2 id="droits-fondamentaux">Droits Fondamentaux Durant la Période de Location</h2>
                        <h3>Droit à la Jouissance Paisible</h3>
                        <ul>
                            <li>Le locataire a le droit de jouir de son logement sans être perturbé injustement, notamment par des bruits excessifs, des entrées non autorisées ou d'autres nuisances.</li>
                        </ul>
                        <h3>Droit à l'Habitabilité</h3>
                        <ul>
                            <li>Le logement doit être en état d'habitabilité, avec des installations sanitaires, électriques et de chauffage en bon état de fonctionnement.</li>
                            <li>Le propriétaire est responsable des réparations majeures et du maintien des éléments structurels du logement.</li>
                        </ul>
                        <h3>Droit à la Protection Contre les Expulsions Injustifiées</h3>
                        <ul>
                            <li>L'expulsion ne peut se faire sans motif valable et sans respecter les procédures légales.</li>
                            <li>Les motifs d'expulsion doivent être légitimes, tels que l'impayé de loyer persistant ou la reprise du logement par le propriétaire pour y habiter personnellement.</li>
                        </ul>
                        <h3>Droit à la Résiliation du Bail</h3>
                        <ul>
                            <li>Le locataire peut résilier son bail selon les conditions contractuelles ou légales, généralement avec un préavis de trois mois pour les baux d'habitation (réduit à un mois dans certaines situations particulières).</li>
                        </ul>
                        <h3>Droit à la Restitution du Dépôt de Garantie</h3>
                        <ul>
                            <li>Le dépôt de garantie doit être restitué dans les deux mois suivant la remise des clés, déduction faite des sommes justifiées par l'état des lieux de sortie comparé à celui d'entrée.</li>
                            <li>Toute retenue sur le dépôt doit être justifiée par des factures ou devis.</li>
                        </ul>

                         <div class="flex justify-center my-8">
                            <img src="../Articles/pict/Vos Droits en Tant que Locataire/Vos Droits en Tant que Locataire - visual selection.jpg"
                                alt="Droits des locataires" class="max-w-full h-auto">
                        </div>
                        <h2 id="information-protection">Droits Concernant l'Information et la Protection</h2>
                        <h3>Droit à l'Information</h3>
                        <ul>
                            <li>Le locataire doit être informé des conditions de location, des charges, des modalités de paiement, et de tout autre élément pertinent avant la signature du bail.</li>
                            <li>Le bail doit mentionner clairement le montant du loyer, les charges, la surface habitable et les équipements inclus.</li>
                        </ul>
                        <h3>Droit à la Protection Contre les Hausses de Loyer Abusives</h3>
                        <ul>
                            <li>Les augmentations de loyer sont réglementées et ne peuvent être imposées qu'une fois par an, selon l'indice de référence des loyers (IRL).</li>
                            <li>Les modalités d'augmentation doivent être prévues dans le contrat de bail.</li>
                        </ul>
                        <h3>Droit à la Sous-Location</h3>
                        <ul>
                            <li>Le locataire peut sous-louer son logement avec l'accord écrit préalable du propriétaire.</li>
                            <li>Le prix du loyer au sous-locataire ne peut excéder celui payé par le locataire principal.</li>
                        </ul>
                        <h3>Droit à la Protection des Biens Personnels</h3>
                        <ul>
                            <li>Le locataire a le droit de conserver ses biens personnels, même en cas d'expulsion, sauf procédure de saisie légale par un huissier.</li>
                        </ul>
                        <h3>Droit aux Animaux Domestiques</h3>
                        <ul>
                            <li>Le locataire peut détenir un animal domestique dans son logement, sauf si celui-ci cause des dégâts importants ou trouble la tranquillité du voisinage.</li>
                            <li>Les clauses d'interdiction totale des animaux sont réputées non écrites, sauf pour les chiens de catégories 1 et 2.</li>
                        </ul>
                         <div class="flex justify-center my-8">
                            <img src="../Articles/pict/Vos Droits en Tant que Locataire/Vos Droits en Tant que Locataire - visual selection (1).jpg"
                                alt="Droits des locataires" class="max-w-full h-auto">
                        </div>
                        <h2 id="changements-incidents">Droits en Cas de Changements ou d'Incidents</h2>
                        <h3>Protection des Données Personnelles</h3>
                        <ul>
                            <li>Les données personnelles du locataire sont protégées par le RGPD, et le propriétaire ne peut les divulguer à des tiers sans consentement explicite.</li>
                        </ul>
                        <h3>Protection Contre les Nuisances</h3>
                        <ul>
                            <li>Le locataire est en droit d'exiger un environnement de vie exempt de nuisances excessives (bruit, pollution, insalubrité).</li>
                            <li>Le propriétaire doit prendre les mesures nécessaires pour faire cesser les troubles causés par d'autres locataires.</li>
                        </ul>
                        <h3>Accès aux Documents</h3>
                        <ul>
                            <li>Le locataire a droit à des copies du bail, des états des lieux, des quittances de loyer et autres documents liés à la location.</li>
                        </ul>
                        <h3>Protection en Cas de Vente du Bien</h3>
                        <ul>
                            <li>Si le propriétaire vend le logement, le locataire bénéficie d'un droit de préemption (priorité d'achat).</li>
                            <li>En cas de vente à un tiers, le bail se poursuit aux mêmes conditions avec le nouveau propriétaire.</li>
                        </ul>
                        <h3>Protection en Cas de Succession</h3>
                        <ul>
                            <li>En cas de décès du locataire, certains proches (conjoint, partenaire PACS, concubin notoire, descendants, ascendants) peuvent poursuivre le bail ou y mettre fin avec un préavis réduit.</li>
                        </ul>
                        <h3>Droits Relatifs aux Travaux</h3>
                        <ul>
                            <li>Le propriétaire doit obtenir l'accord du locataire pour des travaux affectant plus de 5% de la surface du logement.</li>
                            <li>Pour des travaux urgents, le locataire doit les supporter mais peut demander une réduction de loyer si les travaux durent plus de 21 jours.</li>
                        </ul>
                        <h3>Protection en Cas de Sinistre</h3>
                        <ul>
                            <li>En cas de sinistre rendant le logement inhabitable, le locataire peut demander la résiliation du bail sans préavis.</li>
                            <li>L'assurance habitation obligatoire couvre les dommages causés aux biens du locataire.</li>
                        </ul>
                        <h3>Protection en Cas d'Insolvabilité du Propriétaire</h3>
                        <ul>
                            <li>Les procédures collectives concernant le propriétaire n'affectent pas le bail en cours, qui se poursuit avec le repreneur éventuel.</li>
                        </ul>
                        <div class="flex justify-center my-8">
                            <img src="../Articles/pict/Vos Droits en Tant que Locataire/Vos Droits en Tant que Locataire - visual selection (2).jpg"
                                alt="Droits des locataires" class="max-w-full h-auto">
                        </div>
                        <h2 id="violations-discrimination">Droits en Cas de Violations ou de Discrimination</h2>
                        <h3>Recours en Cas de Violation des Droits</h3>
                        <ul>
                            <li>Le locataire peut saisir la commission départementale de conciliation avant toute action judiciaire.</li>
                            <li>Le tribunal judiciaire est compétent pour les litiges locatifs, avec possibilité d'aide juridictionnelle selon les revenus.</li>
                        </ul>
                        <h3>Protection Contre les Discriminations</h3>
                        <ul>
                            <li>Le locataire est protégé contre toute discrimination liée à son origine, son sexe, sa situation familiale, son handicap, ses opinions, etc.</li>
                            <li>Les refus de location discriminatoires sont passibles de sanctions pénales.</li>
                        </ul>

                        <h2 id="conclusion">Conclusion</h2>
                        <p>Il est essentiel pour les locataires de connaître leurs droits et de savoir vers qui se tourner en cas de problème. Les mairies, les Agences Départementales d'Information sur le Logement (ADIL), les associations de locataires et les services juridiques peuvent fournir aide et conseils. La compréhension de ces droits permet une location sereine et conforme aux lois en vigueur.</p>
                        <p>Si vous rencontrez des difficultés ou estimez que vos droits sont bafoués, n'hésitez pas à consulter ces organismes ou un professionnel du droit spécialisé en droit immobilier.</p>
                    </div>
  
                </article>
            </main>
            <!-- Image Placeholder -->
            <aside class="lg:w-1/3" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                <div class="bg-white rounded-xl shadow-md p-6 sticky top-20 h-fit">
                    <h2 class="text-xl font-bold mb-6 text-gray-900">Sommaire</h2>
                    <div class="space-y-4">
                        <a href="#droits-fondamentaux" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Droits Fondamentaux Durant la Période de Location
                        </a>
                        <a href="#information-protection" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Droits Concernant l'Information et la Protection
                        </a>
                        <a href="#changements-incidents" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Droits en Cas de Changements ou d'Incidents
                        </a>
                        <a href="#violations-discrimination" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Droits en Cas de Violations ou de Discrimination
                        </a>
                        <a href="#conclusion" class="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-200 group font-bold">
                            <i class="fas fa-arrow-right text-blue-500 mr-3 transform transition-transform duration-200 group-hover:translate-x-1"></i>
                            Conclusion
                        </a>
                    </div>
                </div>
                <!-- New Sticky Panel for Advertisement -->
                <div class="bg-white rounded-xl shadow-md p-4 mt-6 text-center">
                    <a href="https://www.questionlegale.info" target="_blank" rel="noopener noreferrer">
                        <img src="../img/pub/pub1_QL.jpg" alt="Publicité QuestionLegale.info" class="h-auto rounded-lg mx-auto block">
                    </a>
                </div>
            </aside>
        </div>
    </section>


   
    <!-- Footer -->
    <footer class="bg-gradient-to-b from-gray-100 to-gray-200 text-gray-800" data-aos="fade-up">

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 py-16 md:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
                <!-- Column 1: About -->
                <div>
                    <div class="mb-6">
                        <img src="../img/logos/main1.png" alt="QuestionLegale.info" class="h-10 mb-4">
                        <h3 class="text-xl font-bold text-gray-800">QuestionLegale.Info</h3>
                    </div>
                    <p class="text-gray-600 mb-6">Votre plateforme d'information juridique utilisant l'intelligence
                        artificielle pour répondre à vos questions légales en France.</p>


                </div>

                <!-- Column 2: Menu -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Navigation</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="#analyse"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Poser une question
                            </a>
                        </li>
                        <li>
                            <a href="articleslist.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Les Articles
                            </a>
                        </li>
                        <li>
                            <a href="#comment"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Comment ça marche?
                            </a>
                        </li>
                        <li>
                            <a href="#professionnels"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Trouver un Professionnel
                            </a>
                        </li>
                        <li>
                            <a href="#utiles"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Sites utiles
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 3: À propos -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">À propos</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Mentions légales
                            </a>
                        </li>
                         <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                               Politique de confidentialité
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Conditions d'utilisation
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Services destinés aux professionnels
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Promotion sur le site web
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 4: Contact -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Contact</h4>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 text-blue-700"></i>
                            <span class="text-gray-600">75003 Paris, France</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-blue-700"></i>
                            <span
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">contact (Chez) questionlegale.info</span>
                        </li>
                        <!-- <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3 text-blue-700"></i>
                            <a href="tel:+33123456789"
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">+33 1 23 45 67
                                89</a>
                        </li> -->
                        <li class="flex items-center">
                            <i class="fas fa-clock mr-3 text-blue-700"></i>
                            <span class="text-gray-600">Lun - Ven: 9h00 - 18h00</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="bg-gray-800 text-gray-300 py-6">
            <div class="max-w-7xl mx-auto px-4 md:px-8 flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0 text-center md:text-left">
                    <p>&copy; 2025 QuestionLegale.Info. Tous droits réservés.</p>
                </div>

                <div class="flex items-center">
                    <span class="text-sm mr-2">Made with</span>
                    <span class="text-red-500 mx-1">❤</span>
                    <span class="text-sm">by <a href="#"
                            class="text-blue-400 hover:text-blue-300 transition-colors duration-300">LaDorure
                            Team</a></span>
                </div>

                <div class="hidden md:flex items-center mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white mx-2 transition-colors duration-300">Mentions
                        légales</a>
                    <span class="text-gray-600">|</span>
                </div>
            </div>
        </div>

        <!-- Back to Top Button -->
        <a href="#" id="back-to-top" aria-label="Retour en haut de page"
            class="fixed bottom-8 right-8 bg-blue-700 text-white p-3 rounded-full shadow-lg hover:bg-blue-800 transition-colors duration-300 opacity-0 invisible">
            <i class="fas fa-arrow-up"></i>
        </a>

        <script>
            // Back to top button functionality
            const backToTopButton = document.getElementById('back-to-top');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.remove('opacity-100', 'visible');
                    backToTopButton.classList.add('opacity-0', 'invisible');
                }
            });

            backToTopButton.addEventListener('click', (e) => {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        </script>
    </footer>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/menu.js"></script>
    <script>
        // Enhanced AOS initialization with more options
        AOS.init({
            easing: 'ease-out-cubic',
            duration: 1000,
            once: false,
            mirror: true,
            anchorPlacement: 'top-bottom',
            offset: 120,
            delay: 100,
            disable: 'mobile'
        });

        // Additional interactive effects
        document.querySelectorAll('.hover-zoom').forEach(link => {
            link.addEventListener('mouseenter', (e) => {
                e.target.style.transition = 'transform 0.3s ease, color 0.3s ease';
            });
        });

        // Refresh AOS on window resize for better responsiveness
        window.addEventListener('resize', function () {
            AOS.refresh();
        });

        function toggleFaq(id) {
            const content = document.getElementById(id);
            const icon = document.getElementById(id + '-icon');

            // Toggle content visibility
            content.classList.toggle('hidden');

            // Rotate icon when expanded
            if (content.classList.contains('hidden')) {
                icon.classList.remove('rotate-180');
            } else {
                icon.classList.add('rotate-180');
            }
        }
    </script>
</body>
</html>
