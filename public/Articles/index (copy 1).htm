<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Articles Juridiques - Guides Légaux et Réponses | QuestionLegale.info</title>
    <meta name="description"
        content="Explorez notre vaste collection d'articles juridiques pour des réponses claires et fiables sur le droit français. Guides détaillés sur les amendes, création d'entreprise, contrats de travail, divorce, successions, fiscalité indépendante, et droits des locataires. Idéal pour particuliers et professionnels cherchant à comprendre la législation française.">
    <meta name="author" content="QuestionLegale.info">
    <meta name="keywords"
        content="articles juridiques, droit français, guides légaux, réponses juridiques, contester amende, créer entreprise, contrat travail, divorce, succession, fiscalité indépendants, droits locataire, droit routier, droit du travail, droit de la famille, droit immobilier, QuestionLegale.info, législation française, aide juridique, informations légales">

    <meta property="og:title" content="Articles Juridiques - Guides et Réponses Légales en France | QuestionLegale.info">
    <meta property="og:description"
        content="Explorez notre vaste collection d'articles juridiques pour des réponses claires et fiables sur le droit français. Guides détaillés sur les amendes, création d'entreprise, contrats de travail, divorce, successions, fiscalité indépendante, et droits des locataires. Idéal pour particuliers et professionnels cherchant à comprendre la législation française.">
    <meta property="og:image" content="../img/logo.png">
    <meta property="og:url" content="https://www.questionlegale.info">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="fr_FR">
    <meta property="og:site_name" content="QuestionLegale.info">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

    <!-- Local JS (detecteur francais, localisation avocat naotaire) -->
    <script src="../js/main.js"></script>

    <!-- Basic Favicon -->
    <link rel="icon" href="../img/favicons/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../img/favicons/favicon.ico" type="image/x-icon">

    <!-- Modern Browsers -->
    <link rel="icon" type="image/png" sizes="32x32" href="../img/favicons/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="../img/favicons/favicon-16x16.png">

    <!-- Apple Devices -->
    <link rel="apple-touch-icon" sizes="180x180" href="../img/favicons/apple-touch-icon.png">
    <link rel="mask-icon" href="../img/favicons/safari-pinned-tab.svg" color="#5bbad5">

    <!-- Android Chrome -->
    <link rel="manifest" href="../img/favicons/site.webmanifest">
    <meta name="theme-color" content="#ffffff">

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="msapplication-config" content="../img/favicons/browserconfig.xml">
    <meta name="msapplication-TileImage" content="../img/favicons/mstile-144x144.png">

    <!-- SVG Favicon (Optional) -->
    <link rel="icon" type="image/svg+xml" href="../img/favicons/favicon.svg">

    <!-- Theme Colors for Different Platforms -->
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
    <meta name="theme-color" content="#000000" media="(prefers-color-scheme: dark)">
    <meta itemprop="image" content="../img/favicons/logo.png">


    <style>
        /* Custom styles from ArticleTemplate.html */
        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: #3B82F6;
            z-index: 50;
            transition: width 0.2s ease;
        }
        .image-hover-zoom {
            overflow: hidden;
        }
        .image-hover-zoom img {
            transition: transform 0.5s ease;
        }
        .group:hover .image-hover-zoom img {
            transform: scale(1.1);
        }
        .placeholder {
            position: relative;
            background: #f3f4f6;
            overflow: hidden;
        }
        .placeholder::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 50%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: loading 1.5s infinite;
        }
        @keyframes loading {
            100% {
                left: 100%;
            }
        }
        section {
            background-color: rgba(255, 255, 255, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }
        section:hover {
            opacity: 1;
            transform: translateY(-5px);
        }
        section>div {
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }
        section:hover>div {
            opacity: 1;
        }
        button i {
            border-radius: 20px;
        }
        button {
            border-radius: 20px;
        }
        .textarea-zoom {
            transition: transform 0.3s ease;
        }
        .textarea-zoom:focus {
            transform: scale(1.05);
        }
        .text-5xl {
            font-size: 2.5rem;
        }
        .textarea-zoom:focus {
            outline: 2px solid #007bff;
            box-shadow: 0 0 5px #007bff;
        }
        @media (max-width: 768px) {
            .text-5xl {
                font-size: 2rem;
            }
        }
        @media (max-width: 768px) {
            #home {
                height: auto !important;
                min-height: 85vh;
            }
            #home .flex-col {
                padding-bottom: 1.5rem;
            }
        }
        /* Styles from blog-list.html */
        .card-wrapper {
            perspective: 1000px;
            transition: transform 0.3s ease;
        }
        .card {
            transform-origin: center center;
            transition: all 0.3s ease;
            will-change: transform;
            transform: scale(1);
        }
        .card-wrapper:hover .card {
            transform: scale(1.05);
            box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.2), 0 8px 10px -6px rgb(0 0 0 / 0.2);
        }
    </style>
</head>
<body class="bg-gray-200 text-gray-800 h-full">
    <div class="reading-progress" id="reading-progress"></div>

    <!-- Sticky Menu -->
    <nav
        class="fixed top-0 md:top-2 left-0 md:left-1/2 transform md:-translate-x-1/2 bg-white opacity-90 md:rounded-full shadow-xl px-4 md:px-6 py-1.5 flex items-center justify-between w-full md:w-[90%] md:max-w-5xl z-50">
        <!-- Left: Icon and Title -->
        <a href="../index.html" tooltip="Question Legale" ><div class="flex items-center space-x-2">
            <!-- Icon: replace emoji with SVG or image if needed -->
            <img src="../img/logos/main1.png" alt="Justice.fr" class="w-6 h-6">
            <div class="font-bold text-sm md:text-lg">QuestionLegale.Info</div>

    </div></a>

        <!-- Mobile Section: Burger and Buttons -->
        <div class="flex items-center md:hidden space-x-3">
            <!-- Buttons: visible on mobile -->
            <button id="mobileSignIn" type="button"
                class="px-2 py-0.5 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-xs whitespace-nowrap">
                Se Connecter
            </button>
            <button id="mobileSignUp" type="button"
                class="px-2 py-0.5 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-xs whitespace-nowrap">
                Inscription
            </button>
            <!-- Burger menu button -->
            <button id="burgerBtn" type="button" class="focus:outline-none p-0.5" aria-label="Toggle menu">
                <svg class="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>

        <!-- Center: Menu Items (Desktop) -->
        <div id="menuItems" class="hidden md:flex space-x-6 mx-auto w-full max-w-md justify-center">
            <a href="../index.html#analyse" class="text-gray-700 hover:text-blue-500 font-medium text-sm">Question?</a>
            <a href="articleslist.html"
                class="text-gray-700 hover:text-blue-500 font-medium text-sm">Articles</a>
            <a href="../index.html#references" class="text-gray-700 hover:text-blue-500 font-medium text-sm">References</a>
        </div>

        <!-- Right: Sign In and Sign Up Buttons (Desktop) -->
        <div class="hidden md:flex items-center space-x-2">
            <button id="signInBtn" type="button"
                class="px-3 py-1 border border-blue-500 text-blue-500 rounded-full hover:bg-blue-50 transition text-sm whitespace-nowrap">
                Se Connecter
            </button>
            <button id="signUpBtn" type="button"
                class="px-3 py-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition text-sm whitespace-nowrap">
                Inscription
            </button>
        </div>
    </nav>

    <!-- Mobile Menu: Only for menu items (dropdown) -->
    <div id="mobileMenu"
        class="fixed top-[45px] left-0 w-full bg-white shadow-md rounded-b-lg p-4 md:hidden hidden z-40">
        <div class="flex flex-col space-y-3">
            <a href="../index.html#home" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Question</a>
            <a href="../index.html#about" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">References</a>
            <a href="../index.html#services" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Services</a>
            <a href="../index.html#contact" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Contact</a>
            <a href="../index.html#contact" class="text-gray-700 hover:text-blue-500 font-medium"
                onclick="toggleMobileMenu()">Contact</a>
        </div>
        <!-- Buttons in mobile menu -->
        <div class="flex space-x-3 mt-4">
            <button type="button"
                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-xs font-semibold rounded-full px-3 py-1 transition whitespace-nowrap">
                Se Connecter
            </button>
            <button type="button"
                class="flex-1 bg-red-600 hover:bg-red-700 text-white text-xs font-semibold rounded-full px-3 py-1 transition whitespace-nowrap">
                Inscription
            </button>
        </div>
    </div>

    <!-- Main content for articles list -->
    <section class="relative mb-12 py-16 min-h-screen pt-24">
        <!-- Background image with overlay -->
        <div class="absolute inset-0 presentation-card bg-cover bg-right-top bg-no-repeat" data-aos="fade"
            data-aos-duration="1200">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>
        <div class="container mx-auto px-4 py-8 relative z-10"> <!-- Added relative z-10 to keep content above background -->
            <div class="bg-white bg-opacity-80 rounded-3xl p-8 mb-12 mx-auto flex items-center space-x-4" data-aos="fade-down">
                <img src="../img/logos/logorobot8.png" alt="QuestionLegale.info Logo" class="w-20 h-20 object-contain">
                <p class="text-lg text-gray-700 flex-grow">Explorez notre collection d'articles juridiques pour trouver des réponses claires et fiables à vos questions. Que vous soyez un particulier ou un professionnel, nos guides détaillés vous aideront à mieux comprendre le droit français.</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Article Cards will be inserted here -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-b from-gray-100 to-gray-200 text-gray-800" data-aos="fade-up">

        <!-- Main Footer Content -->
        <div class="max-w-7xl mx-auto px-4 py-16 md:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
                <!-- Column 1: About -->
                <div>
                    <div class="mb-6">
                        <img src="../img/logos/main1.png" alt="QuestionLegale.info" class="h-10 mb-4">
                        <h3 class="text-xl font-bold text-gray-800">QuestionLegale.Info</h3>
                    </div>
                    <p class="text-gray-600 mb-6">Votre plateforme d'information juridique utilisant l'intelligence
                        artificielle pour répondre à vos questions légales en France.</p>


                </div>

                <!-- Column 2: Menu -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Navigation</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="../index.html#analyse"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Poser une question
                            </a>
                        </li>
                        <li>
                            <a href="articleslist.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Les Articles
                            </a>
                        </li>
                        <li>
                            <a href="../index.html#comment"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Comment ça marche?
                            </a>
                        </li>
                        <li>
                            <a href="../index.html#professionnels"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Trouver un Professionnel
                            </a>
                        </li>
                        <li>
                            <a href="../index.html#utiles"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Sites utiles
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 3: À propos -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">À propos</h4>
                    <ul class="space-y-3">
                        <li>
                            <a href="../mentions.html"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Mentions légales
                            </a>
                        </li>
                         <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                               Politique de confidentialité
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Conditions d'utilisation
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Services destinés aux professionnels
                            </a>
                        </li>
                        <li>
                            <a href="#"
                                class="text-gray-600 hover:text-blue-700 flex items-center transition-colors duration-300">
                                <i class="fas fa-chevron-right text-xs mr-2 text-blue-700"></i>
                                Promotion sur le site web
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Column 4: Contact -->
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-gray-800 border-b border-gray-300 pb-2">Contact</h4>
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 mr-3 text-blue-700"></i>
                            <span class="text-gray-600">75003 Paris, France</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-3 text-blue-700"></i>
                            <span
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">contact (Chez) questionlegale.info</span>
                        </li>
                        <!-- <li class="flex items-center">
                            <i class="fas fa-phone-alt mr-3 text-blue-700"></i>
                            <a href="tel:+33123456789"
                                class="text-gray-600 hover:text-blue-700 transition-colors duration-300">+33 1 23 45 67
                                89</a>
                        </li> -->
                        <li class="flex items-center">
                            <i class="fas fa-clock mr-3 text-blue-700"></i>
                            <span class="text-gray-600">Lun - Ven: 9h00 - 18h00</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Bottom Bar -->
        <div class="bg-gray-800 text-gray-300 py-6">
            <div class="max-w-7xl mx-auto px-4 md:px-8 flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0 text-center md:text-left">
                    <p>&copy; 2025 QuestionLegale.Info. Tous droits réservés.</p>
                </div>

                <div class="flex items-center">
                    <span class="text-sm mr-2">Made with</span>
                    <span class="text-red-500 mx-1">❤</span>
                    <span class="text-sm">by <a href="#"
                            class="text-blue-400 hover:text-blue-300 transition-colors duration-300">LaDorure
                            Team</a></span>
                </div>

                <div class="hidden md:flex items-center mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white mx-2 transition-colors duration-300">Mentions
                        légales</a>
                    <span class="text-gray-600">|</span>
                </div>
            </div>
        </div>

        <!-- Back to Top Button -->
        <a href="#" id="back-to-top"
            class="fixed bottom-8 right-8 bg-blue-700 text-white p-3 rounded-full shadow-lg hover:bg-blue-800 transition-colors duration-300 opacity-0 invisible" aria-label="Back to top">
            <i class="fas fa-arrow-up"></i>
        </a>

        <script>
            // Back to top button functionality
            const backToTopButton = document.getElementById('back-to-top');

            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.remove('opacity-100', 'visible');
                    backToTopButton.classList.add('opacity-0', 'invisible');
                }
            });

            backToTopButton.addEventListener('click', (e) => {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        </script>
    </footer>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../js/menu.js"></script>
    <script>
        // Enhanced AOS initialization with more options
        AOS.init({
            easing: 'ease-out-cubic',
            duration: 1000,
            once: false,
            mirror: true,
            anchorPlacement: 'top-bottom',
            offset: 120,
            delay: 100,
            disable: 'mobile'
        });

        // Additional interactive effects
        document.querySelectorAll('.hover-zoom').forEach(link => {
            link.addEventListener('mouseenter', (e) => {
                e.target.style.transition = 'transform 0.3s ease, color 0.3s ease';
            });
        });

        // Refresh AOS on window resize for better responsiveness
        window.addEventListener('resize', function () {
            AOS.refresh();
        });

        function toggleFaq(id) {
            const content = document.getElementById(id);
            const icon = document.getElementById(id + '-icon');

            // Toggle content visibility
            content.classList.toggle('hidden');

            // Rotate icon when expanded
            if (content.classList.contains('hidden')) {
                icon.classList.remove('rotate-180');
            } else {
                icon.classList.add('rotate-180');
            }
        }

        // Article data
        const articles = [
            {
                title: "Comment contester une amende",
                excerpt: "Découvrez les démarches et les recours possibles pour contester une amende en France.",
                image: "pict/Comment contester une amende/Designer (1).jpeg",
                link: "Comment-contester-une-amende.htm",
                hashtags: ["#Amende", "#Contestation", "#DroitRoutier", "#ProcédureLégale"]
            },
            {
                title: "Comment créer une entreprise",
                excerpt: "Un guide complet sur les étapes clés pour créer votre entreprise en France.",
                image: "pict/Comment créer une entreprise Feuille de route détaillée/Designer (1).jpeg",
                link: "Comment-créer-une-entreprise.htm",
                hashtags: ["#Entreprise", "#Création", "#StatutJuridique", "#Formalités"]
            },
            {
                title: "Comment rédiger un contrat de travail",
                excerpt: "Apprenez les fondamentaux de la rédaction d'un contrat de travail conforme à la législation française.",
                image: "pict/Comment rédiger un contrat de travail/Designer (1).jpeg",
                link: "Comment-rédiger-un-contrat-de-travail.htm",
                hashtags: ["#ContratTravail", "#DroitDuTravail", "#Rédaction", "#EmployeurSalarié"]
            },
            {
                title: "Conséquences d'un Contrat Non Respecté",
                excerpt: "Comprenez les implications juridiques et les recours en cas de non-respect d'un contrat.",
                image: "pict/Conséquences d'un Contrat Non Respecté/contrat2.jpg",
                link: "Conséquences-d'un-Contrat-Non-Respecté.htm",
                hashtags: ["#Contrat", "#NonRespect", "#Contentieux", "#DroitCivil"]
            },
            {
                title: "Démarches pour Déclarer une Succession",
                excerpt: "Les étapes essentielles pour déclarer une succession et gérer les biens hérités.",
                image: "pict/Démarches pour Déclarer une Succession/Designer (1).jpeg",
                link: "Démarches-pour-Déclarer-une-Succession.htm",
                hashtags: ["#Succession", "#Héritage", "#Démarches", "#DroitDesSuccessions"]
            },
            {
                title: "Obligations fiscales des indépendants",
                excerpt: "Un aperçu des principales obligations fiscales pour les travailleurs indépendants en France.",
                image: "pict/Obligations fiscales des indépendants/Designer (1).jpeg",
                link: "Obligations-fiscales-des-indépendants.htm",
                hashtags: ["#Indépendant", "#Fiscalité", "#Impôts", "#AutoEntrepreneur"]
            },
            {
                title: "Vos Droits en Cas de Divorce",
                excerpt: "Informations clés sur vos droits et les procédures en cas de divorce en France.",
                image: "pict/Vos Droits en Cas de Divorce/Designer (1).jpeg",
                link: "Vos-Droits-en-Cas-de-Divorce.htm",
                hashtags: ["#Divorce", "#DroitDeLaFamille", "#Procédure", "#PensionAlimentaire"]
            },
            {
                title: "Vos Droits en cas de Licensiment",
                excerpt: "Connaissez vos droits et les démarches à suivre en cas de licenciement.",
                image: "pict/Vos Droits en Cas de Licenciement/Designer (1).jpeg",
                link: "Vos-Droits-en-cas-de-Licensiment.htm",
                hashtags: ["#Licenciement", "#DroitDuTravail", "#Indemnités", "#Procédure"]
            },
            {
                title: "Vos Droits en Tant que Locataire",
                excerpt: "Un guide sur les droits et devoirs des locataires en France.",
                image: "pict/Vos Droits en Tant que Locataire/Designer (1).jpeg",
                link: "Vos-Droits-en-Tant-que-Locataire.htm",
                hashtags: ["#Locataire", "#Bail", "#Logement", "#DroitImmobilier"]
            },
            {
                title: "Démarches pour Obtenir une Carte de Séjour",
                excerpt: "Guide complet sur les procédures et documents nécessaires pour obtenir une carte de séjour en France.",
                image: "pict/Démarches pour Obtenir une Carte de Séjour/Designer.jpeg",
                link: "Démarches-pour-Obtenir-une-Carte-de-Séjour.htm",
                hashtags: ["#CarteDeSéjour", "#Immigration", "#DémarchesAdministratives", "#DroitDesÉtrangers"]
            }
        ];

        const colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#6366F1', '#EC4899', '#06B6D4', '#8B5CF6', '#F97316', '#4CAF50'];

        // Function to darken a hex color
        function darkenColor(hex, percent) {
            let f=parseInt(hex.slice(1),16),t=percent<0?0:255,p=percent<0?percent*-1:percent,R=f>>16,G=(f>>8)&0x00FF,B=f&0x0000FF;
            return "#"+(0x1000000+(Math.round((t-R)*p)+R)*0x10000+(Math.round((t-G)*p)+G)*0x100+(Math.round((t-B)*p)+B)).toString(16).slice(1);
        }

        // Generate article cards
        const gridContainer = document.querySelector('.grid');
        
        articles.forEach((article, index) => {
            const cardWrapper = document.createElement('div');
            cardWrapper.className = 'card-wrapper';
            cardWrapper.setAttribute('data-aos', 'fade-up');
            cardWrapper.setAttribute('data-aos-delay', (index * 100).toString());
            
            const baseColor = colors[index % colors.length];
            const hoverColor = darkenColor(baseColor, 0.1); // Darken by 10%

            const hashtagsHtml = article.hashtags.map(tag => `
                <span class="px-4 py-1.5 text-sm font-semibold text-white rounded-full mr-2 mb-2" 
                      style="background-color: ${baseColor};">
                    ${tag}
                </span>
            `).join('');

            cardWrapper.innerHTML = `
                <div class="card bg-white rounded-3xl overflow-hidden shadow-lg h-full">
                    <div class="flex flex-col h-full">
                        <div class="flex flex-col sm:flex-row flex-grow">
                            <div class="sm:w-2/5 relative overflow-hidden">
                                <img src="${article.image}" alt="${article.title}" class="w-full h-full object-cover">
                            </div>
                            <div class="sm:w-3/5 p-8 flex flex-col">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex flex-wrap">
                                        ${hashtagsHtml}
                                    </div>
                                    <span class="text-sm text-gray-500"></span> <!-- Date removed -->
                                </div>
                                <h2 class="text-xl font-bold mb-3 text-gray-800">${article.title}</h2>
                                <p class="text-gray-600 mb-6 flex-grow">${article.excerpt}</p>
                                <div class="flex justify-end mt-auto">
                                    <a href="${article.link}" 
                                       class="text-white px-6 py-2.5 rounded-full transition duration-300"
                                       style="background-color: ${baseColor};"
                                       onmouseover="this.style.backgroundColor='${hoverColor}'"
                                       onmouseout="this.style.backgroundColor='${baseColor}'">
                                        Lire l'article
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            gridContainer.appendChild(cardWrapper);
        });
    </script>
</body>
</html>
