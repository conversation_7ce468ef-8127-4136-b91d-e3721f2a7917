import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './style.css'

// Import Tailwind CSS
import 'tailwindcss/tailwind.css'

// Import FontAwesome
import '@fortawesome/fontawesome-free/css/all.min.css'

// Import AOS
import AOS from 'aos'
import 'aos/dist/aos.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// Initialize AOS
AOS.init({
  duration: 800,
  once: true,
  offset: 100
})

app.mount('#app')
