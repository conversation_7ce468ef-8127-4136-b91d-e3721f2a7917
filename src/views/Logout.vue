<template>
  <div class="logout-container">
    <LoadingSpinner v-if="loading" message="Déconnexion en cours..." />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import LoadingSpinner from '../components/LoadingSpinner.vue'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(true)

onMounted(async () => {
  try {
    await authStore.signOut()
  } catch (error) {
    console.error('Erreur lors de la déconnexion:', error)
  } finally {
    loading.value = false;
    await nextTick();
    router.push('/')
  }
})
</script>

<style scoped>
.logout-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem;
}
</style>
