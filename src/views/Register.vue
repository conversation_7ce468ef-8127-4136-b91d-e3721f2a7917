<template>
  <div class="container">
    <div class="auth-card">
      <h2>S'inscrire</h2>
      <p class="explanation">Bienvenue sur Question Légale. Pour créer votre compte et accéder à nos services juridiques personnalisés, ve<PERSON><PERSON><PERSON> remplir le formulaire ci-dessous.</p>
      <form @submit.prevent="handleSubmit" class="auth-form">
        <div class="form-group">
          <label for="name">Nom</label>
          <input
            id="name"
            v-model="name"
            type="text"
            required
            placeholder="Entrez votre nom"
          />
        </div>
        <div class="form-group">
          <label for="email">E-mail</label>
          <input
            id="email"
            v-model="email"
            type="email"
            required
            placeholder="Entrez votre e-mail"
          />
        </div>
        <div class="form-group">
          <label for="password">Mot de passe</label>
          <input
            id="password"
            v-model="password"
            type="password"
            required
            placeholder="Entrez votre mot de passe"
            @input="handlePasswordInput"
            @focus="isPasswordFocused = true"
            @blur="isPasswordFocused = false"
          />
        </div>
        <div class="password-requirements" :class="{ 'show': isPasswordFocused || password }">
          <h3>Exigences du mot de passe</h3>
          <ul>
            <li :class="{ valid: !passwordErrors.includes('Le mot de passe doit contenir au moins 8 caractères') }">
              <span class="icon">{{ !passwordErrors.includes('Le mot de passe doit contenir au moins 8 caractères') ? '✓' : '✗' }}</span>
              Au moins 8 caractères
            </li>
            <li :class="{ valid: !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre majuscule') }">
              <span class="icon">{{ !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre majuscule') ? '✓' : '✗' }}</span>
              Une lettre majuscule
            </li>
            <li :class="{ valid: !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre minuscule') }">
              <span class="icon">{{ !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre minuscule') ? '✓' : '✗' }}</span>
              Une lettre minuscule
            </li>
            <li :class="{ valid: !passwordErrors.includes('Le mot de passe doit contenir au moins un chiffre') }">
              <span class="icon">{{ !passwordErrors.includes('Le mot de passe doit contenir au moins un chiffre') ? '✓' : '✗' }}</span>
              Un chiffre
            </li>
          </ul>
        </div>
        <div class="form-group">
          <label for="confirmPassword">Confirmer le mot de passe</label>
          <input
            id="confirmPassword"
            v-model="confirmPassword"
            type="password"
            required
            placeholder="Confirmez votre mot de passe"
          />
        </div>
        <div v-if="error" class="error-message">
          {{ error }}
        </div>
        <div v-if="successMessage" class="success-message">
          {{ successMessage }}
        </div>
        <button type="submit" :disabled="loading || passwordErrors.length > 0">
          {{ loading ? 'Chargement...' : "S'inscrire" }}
        </button>
        <p class="auth-link">
          Vous avez déjà un compte ?
          <router-link to="/login">Connexion</router-link>
        </p>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const name = ref('')
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const error = ref('')
const loading = ref(false)
const successMessage = ref('')
const passwordErrors = ref([])
const isPasswordFocused = ref(false)

function validatePassword(password) {
  const errors = []
  if (password.length < 8) {
    errors.push('Le mot de passe doit contenir au moins 8 caractères')
  }
  if (!/[A-Z]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins une lettre majuscule')
  }
  if (!/[a-z]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins une lettre minuscule')
  }
  if (!/[0-9]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins un chiffre')
  }
  return errors
}

function handlePasswordInput() {
  passwordErrors.value = validatePassword(password.value)
}

async function handleSubmit() {
  try {
    passwordErrors.value = validatePassword(password.value)
    if (passwordErrors.value.length > 0) {
      return
    }

    if (password.value !== confirmPassword.value) {
      error.value = 'Les mots de passe ne correspondent pas'
      return
    }

    passwordErrors.value = passwordErrors.value.map(e => 
      e.replace('Le mot de passe doit contenir', 'Doit contenir')
    )

    loading.value = true
    error.value = ''
    successMessage.value = '' // Clear success message at the start
    const { error: signUpError } = await authStore.signUp({
      email: email.value,
      password: password.value,
      options: {
        data: {
          name: name.value
        }
      }
    })
    
    if (signUpError) {
      error.value = signUpError.message
      return
    }
    
    successMessage.value = 'Inscription réussie ! Veuillez vous connecter.'
    setTimeout(() => {
      router.push('/login')
    }, 1500)
  } catch (err) {
    error.value = "Une erreur s'est produite lors de l'inscription"
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
}

.auth-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.explanation {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 1rem 0;
  text-align: center;
}

input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

button {
  background: #42b983;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover {
  background: #3aa876;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
}

.success-message {
  color: #28a745;
  background-color: #f0fdfa;
  border: 1px solid #b2f2bb;
  padding: 0.75rem;
  margin-bottom: 1rem;
  border-radius: 4px;
}

.auth-link {
  text-align: center;
  margin-top: 1rem;
}

.auth-link a {
  color: #42b983;
  text-decoration: none;
}

.auth-link a:hover {
  text-decoration: underline;
}

.password-requirements {
  margin: 0.5rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.password-requirements h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1rem;
}

.password-requirements ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.password-requirements li {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  color: #dc3545;
  font-size: 0.9rem;
}

.password-requirements li.valid {
  color: #28a745;
}

.password-requirements .icon {
  margin-right: 0.5rem;
  font-weight: bold;
}
</style>
