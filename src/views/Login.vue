<template>
  <div class="container">
    <div class="auth-card">
      <h2>Connexion</h2>
      <p class="explanation">Bienvenue sur Question Légale. Pour accéder à nos services juridiques personnalisés, veuillez vous connecter avec vos identifiants ou utiliser votre compte Google.</p>
      <form @submit.prevent="handleLogin" class="auth-form">
        <div class="form-group">
          <label for="email">E-mail</label>
          <input
            id="email"
            v-model="email"
            type="email"
            required
            placeholder="Entrez votre e-mail"
          />
        </div>
        <div class="form-group">
          <label for="password">Mot de passe</label>
          <input
            id="password"
            v-model="password"
            type="password"
            required
            placeholder="Entrez votre mot de passe"
          />
          <div class="forgot-password">
            <router-link to="/reset-password">Mot de passe oublié ?</router-link>
          </div>
        </div>
        <div v-if="error" class="error-message">
          {{ error }}
        </div>
        <button type="submit" :disabled="isLoading">
          {{ isLoading ? 'Chargement...' : 'Connexion' }}
        </button>
        <p class="auth-link">
          Vous n'avez pas de compte ?
          <router-link to="/register">S'inscrire</router-link>
        </p>
      </form>
      <div class="separator">
        <span>ou</span>
      </div>
      <button type="button" @click="handleGoogleLogin" class="google-btn" :disabled="isLoading">
        <img src="https://www.google.com/favicon.ico" alt="Google" class="google-icon" />
        Se connecter avec Google
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const email = ref('')
const password = ref('')
const error = ref(null)
const isLoading = ref(false)

async function handleGoogleLogin() {
  isLoading.value = true
  error.value = null
  
  try {
    await authStore.signInWithGoogle()
  } catch (err) {
    console.error("Google login error:", err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}

async function handleLogin() {
  isLoading.value = true
  error.value = null
  
  console.log('Email:', email.value); // Log email value
  console.log('Password:', password.value); // Log password value

  try {
    await authStore.signIn({ 
      email: email.value, 
      password: password.value
    })
    router.push('/')
  } catch (err) {
      console.error("Login error:", err); // Log the full error object
      error.value = err.message;
    } finally {
      isLoading.value = false
    }
  }
</script>

<style scoped>
.auth-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

button {
  background: #42b983;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover {
  background: #3aa876;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
}

.auth-link {
  text-align: center;
  margin-top: 1rem;
}

.auth-link a {
  color: #42b983;
  text-decoration: none;
}

.auth-link a:hover {
  text-decoration: underline;
}

.forgot-password {
  text-align: right;
  margin-top: 0.25rem;
}

.forgot-password a {
  color: #42b983;
  text-decoration: none;
  font-size: 0.875rem;
}

.forgot-password a:hover {
  text-decoration: underline;
}

.separator {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 1rem 0;
}

.separator::before,
.separator::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #ddd;
}

.separator span {
  padding: 0 10px;
  color: #666;
  font-size: 0.9rem;
}

.google-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: #fff;
  border: 1px solid #ddd;
  color: #333;
  width: 100%;
}

.google-btn:hover {
  background: #f8f9fa;
}

.google-icon {
  width: 18px;
  height: 18px;
}
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
}

.explanation {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 1rem 0;
  text-align: center;
}
</style>
