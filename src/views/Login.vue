<template>
  <div class="auth-container" data-aos="fade-up" data-aos-duration="800">
    <div class="auth-card bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-xl">
      <!-- Header with logo -->
      <div class="auth-header">
        <div class="flex items-center justify-center mb-6">
          <img src="/img/logos/main1.png" alt="QuestionLegale.Info" class="w-16 h-16 mr-3">
          <div>
            <h2 class="text-2xl font-bold text-gray-800">Connexion</h2>
            <p class="text-sm text-gray-600">QuestionLegale.Info</p>
          </div>
        </div>
        <p class="explanation text-gray-700 text-center mb-6">
          Bienvenue sur Question Légale. Pour accéder à nos services juridiques personnalisés,
          veuillez vous connecter avec vos identifiants ou utiliser votre compte Google.
        </p>
      </div>

      <form @submit.prevent="handleLogin" class="auth-form">
        <div class="form-group">
          <label for="email" class="text-gray-700 font-medium">E-mail</label>
          <input
            id="email"
            v-model="email"
            type="email"
            required
            placeholder="Entrez votre e-mail"
            class="auth-input"
          />
        </div>
        <div class="form-group">
          <label for="password" class="text-gray-700 font-medium">Mot de passe</label>
          <input
            id="password"
            v-model="password"
            type="password"
            required
            placeholder="Entrez votre mot de passe"
            class="auth-input"
          />
          <div class="forgot-password">
            <router-link to="/reset-password" class="text-blue-600 hover:text-blue-800 transition-colors">
              Mot de passe oublié ?
            </router-link>
          </div>
        </div>
        <div v-if="error" class="error-message bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <i class="fas fa-exclamation-circle mr-2"></i>
          {{ error }}
        </div>
        <button type="submit" :disabled="isLoading" class="auth-button primary">
          <i v-if="isLoading" class="fas fa-spinner fa-spin mr-2"></i>
          <i v-else class="fas fa-sign-in-alt mr-2"></i>
          {{ isLoading ? 'Chargement...' : 'Connexion' }}
        </button>
        <p class="auth-link text-center text-gray-600">
          Vous n'avez pas de compte ?
          <router-link to="/register" class="text-red-600 hover:text-red-800 font-medium transition-colors">
            S'inscrire
          </router-link>
        </p>
      </form>

      <div class="separator">
        <div class="separator-line"></div>
        <span class="separator-text bg-white px-4 text-gray-500">ou</span>
        <div class="separator-line"></div>
      </div>

      <button type="button" @click="handleGoogleLogin" class="google-btn" :disabled="isLoading">
        <img src="https://www.google.com/favicon.ico" alt="Google" class="google-icon w-5 h-5 mr-3" />
        <span>Se connecter avec Google</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const email = ref('')
const password = ref('')
const error = ref(null)
const isLoading = ref(false)

async function handleGoogleLogin() {
  isLoading.value = true
  error.value = null
  
  try {
    await authStore.signInWithGoogle()
  } catch (err) {
    console.error("Google login error:", err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}

async function handleLogin() {
  isLoading.value = true
  error.value = null
  
  console.log('Email:', email.value); // Log email value
  console.log('Password:', password.value); // Log password value

  try {
    await authStore.signIn({ 
      email: email.value, 
      password: password.value
    })
    router.push('/')
  } catch (err) {
      console.error("Login error:", err); // Log the full error object
      error.value = err.message;
    } finally {
      isLoading.value = false
    }
  }
</script>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
}

.auth-card {
  padding: 2.5rem;
  width: 100%;
  max-width: 450px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.explanation {
  line-height: 1.6;
  font-size: 0.95rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.auth-input {
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.9);
}

.auth-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.auth-button {
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-button.primary {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  color: white;
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.auth-button.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #dc2626);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

.auth-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.auth-link {
  margin-top: 1rem;
}

.forgot-password {
  text-align: right;
  margin-top: 0.5rem;
}

.forgot-password a {
  font-size: 0.875rem;
  text-decoration: none;
}

.forgot-password a:hover {
  text-decoration: underline;
}

.separator {
  position: relative;
  display: flex;
  align-items: center;
  margin: 2rem 0;
}

.separator-line {
  flex: 1;
  height: 1px;
  background-color: #e5e7eb;
}

.separator-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.google-btn {
  width: 100%;
  padding: 0.875rem 1.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #374151;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.google-btn:hover:not(:disabled) {
  border-color: #d1d5db;
  background-color: #f9fafb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.google-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 768px) {
  .auth-card {
    padding: 2rem;
    margin: 1rem;
  }

  .explanation {
    font-size: 0.9rem;
  }
}
</style>
