<template>
  <div class="auth-container" data-aos="fade-up" data-aos-duration="800">
    <div class="auth-card bg-white bg-opacity-90 backdrop-blur-sm rounded-xl shadow-xl">
      <!-- Header with logo -->
      <div class="auth-header">
        <div class="flex items-center justify-center mb-6">
          <img src="/img/logos/main1.png" alt="QuestionLegale.Info" class="w-16 h-16 mr-3">
          <div>
            <h2 class="text-2xl font-bold text-gray-800">Réinitialiser le mot de passe</h2>
            <p class="text-sm text-gray-600">QuestionLegale.Info</p>
          </div>
        </div>
        <p class="explanation text-gray-700 text-center mb-6">
          Entrez votre adresse e-mail et nous vous enverrons un lien pour réinitialiser votre mot de passe.
        </p>
      </div>

      <form @submit.prevent="handleResetPassword" class="auth-form">
        <div class="form-group">
          <label for="email" class="text-gray-700 font-medium">E-mail</label>
          <input
            id="email"
            v-model="email"
            type="email"
            required
            placeholder="Entrez votre e-mail"
            class="auth-input"
          />
        </div>

        <div v-if="error" class="error-message bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <i class="fas fa-exclamation-circle mr-2"></i>
          {{ error }}
        </div>

        <div v-if="success" class="success-message bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
          <i class="fas fa-check-circle mr-2"></i>
          {{ success }}
        </div>

        <button type="submit" :disabled="isLoading" class="auth-button primary">
          <i v-if="isLoading" class="fas fa-spinner fa-spin mr-2"></i>
          <i v-else class="fas fa-key mr-2"></i>
          {{ isLoading ? 'Chargement...' : 'Réinitialiser le mot de passe' }}
        </button>

        <p class="auth-link text-center text-gray-600">
          Vous vous souvenez de votre mot de passe ?
          <router-link to="/login" class="text-red-600 hover:text-red-800 font-medium transition-colors">
            Retour à la connexion
          </router-link>
        </p>
      </form>

      <div class="separator">
        <div class="separator-line"></div>
        <span class="separator-text bg-white px-4 text-gray-500">ou</span>
        <div class="separator-line"></div>
      </div>

      <button type="button" @click="handleGoogleLogin" class="google-btn" :disabled="isLoading">
        <img src="https://www.google.com/favicon.ico" alt="Google" class="google-icon w-5 h-5 mr-3" />
        <span>Se connecter avec Google</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { supabase } from '../lib/supabase'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const email = ref('')
const error = ref(null)
const success = ref(null)
const isLoading = ref(false)

async function handleGoogleLogin() {
  isLoading.value = true
  error.value = null

  try {
    await authStore.signInWithGoogle()
    router.push('/')
  } catch (err) {
    console.error("Google login error:", err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}

async function handleResetPassword() {
  isLoading.value = true
  error.value = null
  success.value = null

  try {
    const { error: resetError } = await supabase.auth.resetPasswordForEmail(
      email.value,
      {
        redirectTo: `${window.location.origin}/update-password`,
      }
    )

    if (resetError) throw resetError

    success.value = 'Les instructions de réinitialisation ont été envoyées à votre e-mail'
    email.value = ''
  } catch (err) {
    console.error('Reset password error:', err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
}

.auth-card {
  padding: 2.5rem;
  width: 100%;
  max-width: 450px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.explanation {
  line-height: 1.6;
  font-size: 0.95rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.auth-input {
  padding: 0.875rem 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.9);
}

.auth-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.auth-button {
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-button.primary {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  color: white;
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.auth-button.primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #dc2626);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

.auth-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.success-message {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.auth-link {
  margin-top: 1rem;
}

.separator {
  position: relative;
  display: flex;
  align-items: center;
  margin: 2rem 0;
}

.separator-line {
  flex: 1;
  height: 1px;
  background-color: #e5e7eb;
}

.separator-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.google-btn {
  width: 100%;
  padding: 0.875rem 1.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #374151;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.google-btn:hover:not(:disabled) {
  border-color: #d1d5db;
  background-color: #f9fafb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.google-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 768px) {
  .auth-card {
    padding: 2rem;
    margin: 1rem;
  }

  .explanation {
    font-size: 0.9rem;
  }
}
</style>
