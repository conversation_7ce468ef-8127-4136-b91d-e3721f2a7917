<template>
  <div class="auth-container">
    <div class="auth-card">
      <h2>Réinitialiser le mot de passe</h2>
      <form @submit.prevent="handleResetPassword" class="auth-form">
        <div class="form-group">
          <label for="email">E-mail</label>
          <input
            id="email"
            v-model="email"
            type="email"
            required
            placeholder="Entrez votre e-mail"
          />
        </div>
        <div v-if="error" class="error-message">
          {{ error }}
        </div>
        <div v-if="success" class="success-message">
          {{ success }}
        </div>
        <button type="submit" :disabled="isLoading">
          {{ isLoading ? 'Chargement...' : 'Réinitialiser le mot de passe' }}
        </button>
        <p class="auth-link">
          Vous vous souvenez de votre mot de passe ?
          <router-link to="/login">Retour à la connexion</router-link>
        </p>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { supabase } from '../lib/supabase'

const email = ref('')
const error = ref(null)
const success = ref(null)
const isLoading = ref(false)

async function handleResetPassword() {
  isLoading.value = true
  error.value = null
  success.value = null

  try {
    const { error: resetError } = await supabase.auth.resetPasswordForEmail(
      email.value,
      {
        redirectTo: `${window.location.origin}/update-password`,
      }
    )

    if (resetError) throw resetError

    success.value = 'Les instructions de réinitialisation ont été envoyées à votre e-mail'
    email.value = ''
  } catch (err) {
    console.error('Reset password error:', err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 1rem;
}

.auth-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

button {
  background: #42b983;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover {
  background: #3aa876;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
}

.success-message {
  color: #28a745;
  font-size: 0.875rem;
}

.auth-link {
  text-align: center;
  margin-top: 1rem;
}

.auth-link a {
  color: #42b983;
  text-decoration: none;
}

.auth-link a:hover {
  text-decoration: underline;
}
</style>
