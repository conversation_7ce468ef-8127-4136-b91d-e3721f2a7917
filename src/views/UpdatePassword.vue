<template>
  <div class="auth-container">
    <div class="auth-layout">
      <div class="auth-card">
        <h2>Mettre à jour le mot de passe</h2>
        <form @submit.prevent="handleUpdatePassword" class="auth-form">
          <div class="form-group">
            <label for="password">Nouveau mot de passe</label>
            <input
              id="password"
              v-model="password"
              type="password"
              required
              placeholder="Entrez le nouveau mot de passe"
              @input="handlePasswordInput"
              @focus="isPasswordFocused = true"
              @blur="isPasswordFocused = false"
            />
          </div>
          <div class="form-group">
            <label for="confirmPassword">Confirmer le mot de passe</label>
            <input
              id="confirmPassword"
              v-model="confirmPassword"
              type="password"
              required
              placeholder="Confirmez le nouveau mot de passe"
            />
          </div>
          <div v-if="error" class="error-message">
            {{ error }}
          </div>
          <div v-if="success" class="success-message">
            {{ success }}
          </div>
          <button type="submit" :disabled="isLoading || passwordErrors.length > 0">
            {{ isLoading ? 'Chargement...' : 'Mettre à jour le mot de passe' }}
          </button>
        </form>
      </div>
      <div class="password-requirements-panel" :class="{ 'show': isPasswordFocused || password }">
        <h3>Exigences du mot de passe</h3>
        <ul>
          <li :class="{ valid: !passwordErrors.includes('Le mot de passe doit contenir au moins 8 caractères') }">
            <span class="icon">{{ !passwordErrors.includes('Le mot de passe doit contenir au moins 8 caractères') ? '✓' : '✗' }}</span>
            Au moins 8 caractères
          </li>
          <li :class="{ valid: !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre majuscule') }">
            <span class="icon">{{ !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre majuscule') ? '✓' : '✗' }}</span>
            Une lettre majuscule
          </li>
          <li :class="{ valid: !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre minuscule') }">
            <span class="icon">{{ !passwordErrors.includes('Le mot de passe doit contenir au moins une lettre minuscule') ? '✓' : '✗' }}</span>
            Une lettre minuscule
          </li>
          <li :class="{ valid: !passwordErrors.includes('Le mot de passe doit contenir au moins un chiffre') }">
            <span class="icon">{{ !passwordErrors.includes('Le mot de passe doit contenir au moins un chiffre') ? '✓' : '✗' }}</span>
            Un chiffre
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { supabase } from '../lib/supabase'

const router = useRouter()
const password = ref('')
const confirmPassword = ref('')
const error = ref(null)
const success = ref(null)
const isLoading = ref(false)
const passwordErrors = ref([])
const isPasswordFocused = ref(false)

function validatePassword(password) {
  const errors = []
  if (password.length < 8) {
    errors.push('Le mot de passe doit contenir au moins 8 caractères')
  }
  if (!/[A-Z]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins une lettre majuscule')
  }
  if (!/[a-z]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins une lettre minuscule')
  }
  if (!/[0-9]/.test(password)) {
    errors.push('Le mot de passe doit contenir au moins un chiffre')
  }
  return errors
}

function handlePasswordInput() {
  passwordErrors.value = validatePassword(password.value)
}

async function handleUpdatePassword() {
  passwordErrors.value = validatePassword(password.value)
  if (passwordErrors.value.length > 0) {
    return
  }

  if (password.value !== confirmPassword.value) {
    error.value = 'Les mots de passe ne correspondent pas'
    return
  }

  isLoading.value = true
  error.value = null
  success.value = null

  try {
    const { error: updateError } = await supabase.auth.updateUser({
      password: password.value
    })

    if (updateError) throw updateError

    success.value = 'Mot de passe mis à jour avec succès'
    setTimeout(() => {
      router.push('/login')
    }, 2000)
  } catch (err) {
    console.error('Update password error:', err)
    error.value = err.message
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 1rem;
}

.auth-layout {
  display: flex;
  gap: 2rem;
  align-items: flex-start;
  max-width: 800px;
  width: 100%;
}

.auth-card {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

button {
  background: #42b983;
  color: white;
  border: none;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

button:hover {
  background: #3aa876;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
}

.success-message {
  color: #28a745;
  font-size: 0.875rem;
}

.password-requirements-panel {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 300px;
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.3s ease;
  position: sticky;
  top: 1rem;
}

.password-requirements-panel.show {
  opacity: 1;
  transform: translateX(0);
}

.password-requirements-panel h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.password-requirements-panel ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.password-requirements-panel li {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  color: #dc3545;
  transition: color 0.2s ease;
}

.password-requirements-panel li.valid {
  color: #28a745;
}

.password-requirements-panel .icon {
  margin-right: 0.5rem;
  font-weight: bold;
}

@media (max-width: 768px) {
  .auth-layout {
    flex-direction: column;
    align-items: center;
  }

  .password-requirements-panel {
    width: 100%;
    max-width: 400px;
    margin-top: 1rem;
    position: static;
  }
}
</style>
