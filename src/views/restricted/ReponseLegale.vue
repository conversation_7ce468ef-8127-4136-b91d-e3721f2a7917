<template>
  <div class="reponse-legale-container">
    <!-- Header Section -->
    <div class="header-section" data-aos="fade-down" data-aos-duration="800">
      <div class="max-w-7xl mx-auto px-4 py-6">
        <h1 class="text-3xl font-bold text-gray-800 text-center mb-2">Votre Réponse Légale</h1>
        <p class="text-gray-600 text-center">Analyse juridique personnalisée basée sur votre question</p>
      </div>
    </div>

    <!-- Main Content Layout -->
    <div class="main-layout max-w-7xl mx-auto px-4 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

        <!-- Left Content Area -->
        <div class="lg:col-span-2 space-y-6">

          <!-- Question Section -->
          <div class="question-card" data-aos="fade-up" data-aos-duration="800">
            <div class="card-header">
              <div class="flex items-center">
                <i class="fas fa-question-circle text-blue-600 text-xl mr-3"></i>
                <h2 class="text-xl font-semibold text-gray-800">Votre Question</h2>
              </div>
            </div>
            <div class="card-content">
              <div v-if="questionText" class="question-text">
                {{ questionText }}
              </div>
              <div v-else class="no-content">
                <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                Question non disponible
              </div>
            </div>
          </div>

          <!-- Response Section -->
          <div class="response-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
            <div class="card-header">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <i class="fas fa-gavel text-red-600 text-xl mr-3"></i>
                  <h2 class="text-xl font-semibold text-gray-800">Réponse Juridique</h2>
                </div>
                <div v-if="apiResponse" class="flex items-center space-x-2">
                  <button @click="downloadResponse" class="download-btn">
                    <i class="fas fa-download mr-2"></i>
                    Télécharger
                  </button>
                  <button @click="printResponse" class="print-btn">
                    <i class="fas fa-print mr-2"></i>
                    Imprimer
                  </button>
                </div>
              </div>
            </div>
            <div class="card-content">
              <!-- Loading State -->
              <div v-if="isLoading" class="loading-state">
                <div class="flex flex-col items-center justify-center py-12">
                  <LoadingSpinner />
                  <p class="text-gray-600 mt-4">Analyse de votre question en cours...</p>
                  <div class="loading-progress mt-4">
                    <div class="progress-bar"></div>
                  </div>
                </div>
              </div>

              <!-- Error State -->
              <div v-else-if="error" class="error-state">
                <div class="error-content">
                  <i class="fas fa-exclamation-circle text-red-500 text-2xl mb-3"></i>
                  <h3 class="text-lg font-semibold text-red-700 mb-2">Erreur lors de l'analyse</h3>
                  <p class="text-red-600">{{ error }}</p>
                  <button @click="fetchApiResponse" class="retry-btn mt-4">
                    <i class="fas fa-redo mr-2"></i>
                    Réessayer
                  </button>
                </div>
              </div>

              <!-- Success State -->
              <div v-else-if="apiResponse" class="response-content">
                <div class="response-header">
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                      <div class="status-badge success">
                        <i class="fas fa-check-circle mr-2"></i>
                        Analyse terminée
                      </div>
                    </div>
                    <div class="response-meta">
                      <span class="text-sm text-gray-500">
                        <i class="fas fa-clock mr-1"></i>
                        {{ new Date().toLocaleDateString('fr-FR') }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="markdown-content" v-html="markdownToHtml"></div>

                <!-- Legal Warning -->
                <div class="legal-warning">
                  <div class="warning-header">
                    <i class="fas fa-exclamation-triangle text-amber-500 mr-2"></i>
                    <span class="font-semibold text-gray-800">Avertissement Légal</span>
                  </div>
                  <p class="warning-text">
                    Les réponses produites par les LLM sont sujettes aux hallucinations. Vérifiez toutes références juridiques.
                    Contactez un professionnel pour une analyse exacte. Cette réponse ne constitue pas un conseil juridique personnalisé.
                  </p>
                </div>
              </div>

              <!-- Empty State -->
              <div v-else class="empty-state">
                <div class="flex flex-col items-center justify-center py-12">
                  <i class="fas fa-file-alt text-gray-400 text-4xl mb-4"></i>
                  <h3 class="text-lg font-semibold text-gray-600 mb-2">Aucune réponse disponible</h3>
                  <p class="text-gray-500 text-center">Veuillez poser une question pour obtenir une analyse juridique.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Panel -->
        <div class="lg:col-span-1">
          <div class="sticky top-6 space-y-6">

            <!-- User Info Panel -->
            <div class="info-panel" data-aos="fade-left" data-aos-duration="800">
              <div class="panel-header">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                  <i class="fas fa-user-circle text-blue-600 mr-2"></i>
                  Votre Profil
                </h3>
              </div>
              <div class="panel-content">
                <div v-if="user" class="user-info">
                  <div class="user-avatar">
                    <img v-if="user.user_metadata?.avatar_url" :src="user.user_metadata.avatar_url" alt="Avatar" class="w-12 h-12 rounded-full">
                    <div v-else class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                      <i class="fas fa-user text-blue-600"></i>
                    </div>
                  </div>
                  <div class="user-details">
                    <p class="font-medium text-gray-800">{{ user.user_metadata?.full_name || user.email }}</p>
                    <p class="text-sm text-gray-600">{{ user.email }}</p>
                  </div>
                </div>
                <div class="credits-info mt-4">
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">Crédits restants</span>
                    <span class="font-semibold text-green-600">{{ authStore.profile?.credits || 0 }}</span>
                  </div>
                  <div class="progress-bar-container mt-2">
                    <div class="progress-bar-bg">
                      <div class="progress-bar-fill" :style="{ width: `${Math.min((authStore.profile?.credits || 0) / 10 * 100, 100)}%` }"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useAuthStore } from '../../stores/auth';
import { useQuestionStore } from '../../stores/question.js';
import axios from 'axios';
import { useRouter } from 'vue-router';
import LoadingSpinner from '../../components/LoadingSpinner.vue';
import { marked } from 'marked';

const router = useRouter();
const authStore = useAuthStore();
const questionStore = useQuestionStore();
// Keep store refs needed for API call and profile fetch
const { user, api_key, uid } = storeToRefs(authStore);

const questionText = ref(questionStore.questionText.value);
const apiResponse = ref(null);
const isLoading = ref(false);
const error = ref(null);
const apiUrl = import.meta.env.VITE_QUEST_API_URL || 'https://default-fallback-url.com';

const markdownToHtml = computed(() => {
  if (apiResponse.value) {
    return marked(apiResponse.value);
  }
  return '';
});

const fetchApiResponse = async () => {
  if (!user.value || !uid.value || !api_key.value || !questionText.value) {
    error.value = "Erreur: Informations requises manquantes pour l'appel API.";
    console.error("Missing data for API call:", { user: user.value, uid: uid.value, api_key: api_key.value, questionText: questionText.value });
    return;
  }

  isLoading.value = true;
  error.value = null;
  apiResponse.value = null;

  if (!apiUrl || apiUrl === 'https://default-fallback-url.com') {
    error.value = "Erreur: L'URL de l'API n'est pas configurée.";
    console.error("VITE_QUEST_API_URL environment variable is not set.");
    isLoading.value = false;
    return;
  }

  try {
    const response = await axios.post('/legal', { // Use relative path for proxy
      user_id: uid.value,
      api_key: api_key.value,
      query: questionText.value
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    apiResponse.value = response.data.response; // Assuming the response is in data.response
    console.log('Complete API Response:', response);

    // Fetch updated profile (including credits) after successful API call
    await authStore.fetchProfile();

  } catch (err) {
    console.error('Erreur lors de l\'appel API:', err);
    if (err.response) {
      error.value = `Erreur API: ${err.response.status} ${err.response.statusText}. Détails: ${err.response.data}`;
    } else {
      error.value = err.message || 'Une erreur est survenue lors de la communication avec l\'API.';
    }
  } finally {
    isLoading.value = false;
  }
};

// Additional functions for the new features
const downloadResponse = () => {
  if (!apiResponse.value) return;

  const element = document.createElement('a');
  const file = new Blob([apiResponse.value], { type: 'text/plain' });
  element.href = URL.createObjectURL(file);
  element.download = `reponse-legale-${new Date().toISOString().split('T')[0]}.txt`;
  document.body.appendChild(element);
  element.click();
  document.body.removeChild(element);
};

const printResponse = () => {
  if (!apiResponse.value) return;

  const printWindow = window.open('', '_blank');
  printWindow.document.write(`
    <html>
      <head>
        <title>Réponse Légale - QuestionLegale.Info</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
          h1 { color: #333; border-bottom: 2px solid #dc2626; padding-bottom: 10px; }
          h2 { color: #555; margin-top: 30px; }
          .question { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
          .response { margin-top: 20px; }
        </style>
      </head>
      <body>
        <h1>Réponse Légale - QuestionLegale.Info</h1>
        <div class="question">
          <h2>Question posée :</h2>
          <p>${questionText.value}</p>
        </div>
        <div class="response">
          <h2>Réponse juridique :</h2>
          ${markdownToHtml.value}
        </div>
        <p style="margin-top: 40px; font-size: 12px; color: #666;">
          Document généré le ${new Date().toLocaleDateString('fr-FR')} par QuestionLegale.Info
        </p>
      </body>
    </html>
  `);
  printWindow.document.close();
  printWindow.print();
};



onMounted(async () => {
  // Ensure user is loaded before attempting API call
  await authStore.loadUserWithProfile();
  if (authStore.user) {
    fetchApiResponse();
  } else {
    // Redirect to login if user is not authenticated
    router.push('/login');
  }
});

</script>

<style scoped>
/* Container Styles */
.reponse-legale-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.header-section {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Card Styles */
.question-card,
.response-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.question-card:hover,
.response-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.card-content {
  padding: 1.5rem;
}

/* Question Text Styling */
.question-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #374151;
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 12px;
  border-left: 4px solid #3b82f6;
  font-style: italic;
}

.no-content {
  color: #f59e0b;
  font-style: italic;
  padding: 1rem;
  text-align: center;
}

/* Loading State */
.loading-state {
  text-align: center;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin: 0 auto;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  animation: loading 2s ease-in-out infinite;
}

@keyframes loading {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(0%); }
  100% { transform: translateX(100%); }
}

/* Error State */
.error-state {
  text-align: center;
  padding: 2rem;
}

.error-content {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  padding: 2rem;
  display: inline-block;
}

.retry-btn {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.retry-btn:hover {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

/* Success State */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-badge.success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

/* Enhanced Markdown Content Styling */
.markdown-content {
  line-height: 1.8;
  color: #374151;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 1.05rem;
  max-width: none;
}

/* Typography Hierarchy */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-weight: 700;
  line-height: 1.3;
  margin-top: 2.5rem;
  margin-bottom: 1.25rem;
  scroll-margin-top: 2rem;
}

.markdown-content h1 {
  font-size: 2.25rem;
  color: #1f2937;
  border-bottom: 3px solid #dc2626;
  padding-bottom: 0.75rem;
  margin-bottom: 2rem;
  position: relative;
}

.markdown-content h1::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #dc2626, #ef4444);
  border-radius: 2px;
}

.markdown-content h2 {
  font-size: 1.75rem;
  color: #dc2626;
  position: relative;
  padding-left: 1rem;
  border-left: 4px solid #dc2626;
  background: linear-gradient(90deg, #fef2f2, transparent);
  padding: 0.75rem 0 0.75rem 1rem;
  border-radius: 0 8px 8px 0;
  margin: 2rem 0 1.5rem 0;
}

.markdown-content h3 {
  font-size: 1.375rem;
  color: #3b82f6;
  position: relative;
  padding-left: 0.75rem;
  border-left: 3px solid #3b82f6;
  background: linear-gradient(90deg, #eff6ff, transparent);
  padding: 0.5rem 0 0.5rem 0.75rem;
  border-radius: 0 6px 6px 0;
}

.markdown-content h4 {
  font-size: 1.25rem;
  color: #059669;
  font-weight: 600;
}

.markdown-content h5 {
  font-size: 1.125rem;
  color: #7c3aed;
  font-weight: 600;
}

.markdown-content h6 {
  font-size: 1rem;
  color: #dc2626;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Paragraph and Text Styling */
.markdown-content p {
  margin-bottom: 2.5rem;
  text-align: justify;
  text-justify: inter-word;
  hyphens: auto;
  line-height: 1.8;
}

.markdown-content p:first-of-type {
  font-size: 1.125rem;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.7;
}

/* Enhanced Lists */
.markdown-content ul,
.markdown-content ol {
  margin-bottom: 2rem;
  padding-left: 2rem;
  list-style-position: outside;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content ul li,
.markdown-content ol li {
  margin-bottom: 0.75rem;
  padding-left: 0.5rem;
  line-height: 1.7;
}

.markdown-content ul li::marker {
  color: #dc2626;
  font-size: 1.2rem;
}

.markdown-content ol li::marker {
  color: #dc2626;
  font-weight: bold;
}

/* Nested Lists */
.markdown-content ul ul,
.markdown-content ol ol,
.markdown-content ul ol,
.markdown-content ol ul {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  margin-left: 1rem;
}

.markdown-content ul ul {
  list-style-type: circle;
}

.markdown-content ul ul ul {
  list-style-type: square;
}

/* Enhanced Blockquotes */
.markdown-content blockquote {
  border-left: 5px solid #3b82f6;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  padding: 1.5rem 2rem;
  margin: 2rem 0;
  border-radius: 0 12px 12px 0;
  font-style: italic;
  position: relative;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.markdown-content blockquote::before {
  content: '"';
  position: absolute;
  top: 0.5rem;
  left: 1rem;
  font-size: 3rem;
  color: #3b82f6;
  opacity: 0.3;
  font-family: Georgia, serif;
}

.markdown-content blockquote p {
  margin-bottom: 0;
  font-size: 1.1rem;
  color: #1f2937;
  padding-left: 2rem;
}

/* Code Styling */
.markdown-content code {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  color: #dc2626;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid #fecaca;
}

.markdown-content pre {
  background: linear-gradient(135deg, #1f2937, #111827);
  color: #f9fafb;
  padding: 2rem;
  border-radius: 12px;
  overflow-x: auto;
  margin: 2rem 0;
  border: 1px solid #374151;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
}

.markdown-content pre::before {
  content: 'Code';
  position: absolute;
  top: 0.5rem;
  right: 1rem;
  font-size: 0.75rem;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.markdown-content pre code {
  background: none;
  color: inherit;
  padding: 0;
  border-radius: 0;
  border: none;
  font-size: 0.95rem;
}

/* Text Emphasis */
.markdown-content strong {
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  padding: 0.1rem 0.3rem;
  border-radius: 4px;
}

.markdown-content em {
  font-style: italic;
  color: #6b7280;
  font-weight: 500;
}

/* Tables */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.markdown-content th,
.markdown-content td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.markdown-content th {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  font-weight: 600;
  color: #1f2937;
  border-bottom: 2px solid #dc2626;
}

.markdown-content tr:hover {
  background: #f8fafc;
}

/* Links */
.markdown-content a {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid transparent;
  transition: all 0.3s ease;
}

.markdown-content a:hover {
  color: #1d4ed8;
  border-bottom-color: #3b82f6;
}

/* Horizontal Rules */
.markdown-content hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, #dc2626, transparent);
  margin: 3rem 0;
  border-radius: 1px;
}

/* Special Content Boxes */
.markdown-content .info-box {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  border: 1px solid #93c5fd;
  border-left: 4px solid #3b82f6;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 2rem 0;
}

.markdown-content .warning-box {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #fbbf24;
  border-left: 4px solid #f59e0b;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 2rem 0;
}

.markdown-content .danger-box {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border: 1px solid #fca5a5;
  border-left: 4px solid #dc2626;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 2rem 0;
}

/* Action Buttons */
.download-btn,
.print-btn {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
}

.download-btn:hover,
.print-btn:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.print-btn {
  background: linear-gradient(135deg, #059669, #047857);
}

.print-btn:hover {
  background: linear-gradient(135deg, #047857, #065f46);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.4);
}

/* Right Panel Styles */
.info-panel {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-panel:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.panel-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.panel-content {
  padding: 1.5rem;
}

/* User Info Styles */
.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar img,
.user-avatar div {
  border: 2px solid #e2e8f0;
}

.credits-info {
  background: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.progress-bar-container {
  width: 100%;
}

.progress-bar-bg {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Action Buttons */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
}

.action-btn.primary {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
}

.action-btn.primary:hover {
  background: linear-gradient(135deg, #b91c1c, #991b1b);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

.action-btn.secondary {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
}

.action-btn.secondary:hover:not(:disabled) {
  background: linear-gradient(135deg, #4b5563, #374151);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
}

.action-btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Help Links */
.help-link {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  color: #6b7280;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.help-link:hover {
  background: #f8fafc;
  color: #374151;
  transform: translateX(4px);
}

/* Empty State */
.empty-state {
  text-align: center;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .main-layout {
    padding: 1rem;
  }

  .sticky {
    position: static;
  }

  .info-panel {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 768px) {
  .header-section {
    padding: 1rem 0;
  }

  .card-header,
  .card-content,
  .panel-header,
  .panel-content {
    padding: 1rem;
  }

  .question-text {
    font-size: 1rem;
    padding: 1rem;
  }

  .markdown-content h1 {
    font-size: 1.5rem;
  }

  .markdown-content h2 {
    font-size: 1.25rem;
  }

  .markdown-content h3 {
    font-size: 1.125rem;
  }

  .download-btn,
  .print-btn {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .user-info {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
}
</style>
