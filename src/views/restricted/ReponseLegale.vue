<template>
  <div class="reponse-legale-view">
    <h1>Votre Réponse Légale</h1>

    <div class="info-section">
      <h2>Votre Question</h2>
      <p v-if="questionText">{{ questionText }}</p>
      <p v-else>Question text not available.</p>
    </div>

    <div class="info-section">
      <h2>Réponse de l'API</h2>
      <div v-if="isLoading">
        <LoadingSpinner />
      </div>
      <div v-else-if="error" class="error-message">
        <p>Erreur: {{ error }}</p>
      </div>
      <div v-else-if="apiResponse" class="api-response-content" v-html="markdownToHtml">
      </div>
      <div v-else>
        <p>Aucune réponse disponible.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useAuthStore } from '../../stores/auth';
import { useQuestionStore } from '../../stores/question.js';
import axios from 'axios';
import { useRouter } from 'vue-router';
import LoadingSpinner from '../../components/LoadingSpinner.vue';
import { marked } from 'marked';

const router = useRouter();
const authStore = useAuthStore();
const questionStore = useQuestionStore();
// Keep store refs needed for API call and profile fetch
const { user, api_key, uid } = storeToRefs(authStore);

const questionText = ref(questionStore.questionText.value);
const apiResponse = ref(null);
const isLoading = ref(false);
const error = ref(null);
const apiUrl = import.meta.env.VITE_QUEST_API_URL || 'https://default-fallback-url.com';

const markdownToHtml = computed(() => {
  if (apiResponse.value) {
    return marked(apiResponse.value);
  }
  return '';
});

const fetchApiResponse = async () => {
  if (!user.value || !uid.value || !api_key.value || !questionText.value) {
    error.value = "Erreur: Informations requises manquantes pour l'appel API.";
    console.error("Missing data for API call:", { user: user.value, uid: uid.value, api_key: api_key.value, questionText: questionText.value });
    return;
  }

  isLoading.value = true;
  error.value = null;
  apiResponse.value = null;

  if (!apiUrl || apiUrl === 'https://default-fallback-url.com') {
    error.value = "Erreur: L'URL de l'API n'est pas configurée.";
    console.error("VITE_QUEST_API_URL environment variable is not set.");
    isLoading.value = false;
    return;
  }

  try {
    const response = await axios.post('/legal', { // Use relative path for proxy
      user_id: uid.value,
      api_key: api_key.value,
      query: questionText.value
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    apiResponse.value = response.data.response; // Assuming the response is in data.response
    console.log('Complete API Response:', response);

    // Fetch updated profile (including credits) after successful API call
    await authStore.fetchProfile();

  } catch (err) {
    console.error('Erreur lors de l\'appel API:', err);
    if (err.response) {
      error.value = `Erreur API: ${err.response.status} ${err.response.statusText}. Détails: ${err.response.data}`;
    } else {
      error.value = err.message || 'Une erreur est survenue lors de la communication avec l\'API.';
    }
  } finally {
    isLoading.value = false;
  }
};

onMounted(async () => {
  // Ensure user is loaded before attempting API call
  await authStore.loadUserWithProfile();
  if (authStore.user) {
    fetchApiResponse();
  } else {
    // Redirect to login if user is not authenticated
    router.push('/login');
  }
});

</script>

<style scoped>
.reponse-legale-view {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
}

h1 {
  text-align: center;
  margin-bottom: 20px;
}

.info-section {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 5px;
  background-color: #fff;
}

.info-section h2 {
  margin-top: 0;
  color: #007bff;
}

strong {
  margin-right: 5px;
}

.error-message {
  color: #d9534f; /* Bootstrap danger color */
  background-color: #f2dede; /* Light red background */
  border: 1px solid #ebccd1; /* Reddish border */
  padding: 0.75rem 1.25rem;
  border-radius: 4px;
  margin-top: 1rem; /* Add some space above the error message */
}

.api-response-content {
  text-align: left;
}
</style>
