import { defineStore } from 'pinia'
import { supabase } from '../lib/supabase'
import { ref, computed } from 'vue'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const loading = ref(true)
  const isAuthenticated = computed(() => !!user.value)
  const session = ref(null)
  const credits = ref(0)
  const api_key = ref(null)
  const authAction = ref(null)
  const uid = ref(null)
  const questionText = ref(null) // New state property for question text

 // Initialize the store with the current session
  async function initialize() {
    try {
      // Get initial session
      const { data: { session: currentSession } } = await supabase.auth.getSession()
      session.value = currentSession
      user.value = currentSession?.user ?? null
      uid.value = currentSession?.user?.id ?? null

      // Fetch profile data if user is logged in
      if (currentSession?.user) {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('credits, api_key')
          .eq('user_id', currentSession.user.id)
          .single()

        if (!profileError && profileData) {
          credits.value = profileData.credits || 0
          api_key.value = profileData.api_key
        }
      }

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, newSession) => {
        session.value = newSession
        user.value = newSession?.user ?? null
        uid.value = newSession?.user?.id ?? null
        
        // Set auth action for newly authenticated users
        if (event === 'SIGNED_IN') {
          authAction.value = 'login'
        } else if (event === 'SIGNED_OUT') {
          authAction.value = 'logout'
        }

        // Update profile data when session changes
        if (newSession?.user) {
          // First try to get existing profile
          let { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('credits, api_key')
            .eq('user_id', newSession.user.id)
            .single()

          // If no profile exists, create one (especially for Google users)
          if (profileError || !profileData) {
            const { data: newProfile, error: insertError } = await supabase
              .from('profiles')
              .insert([
                { 
                  user_id: newSession.user.id,
                  credits: 3,
                  api_key: crypto.randomUUID()
                }
              ])
              .select()
              .single()

            if (!insertError && newProfile) {
              profileData = newProfile
            }
          }

          if (profileData) {
            credits.value = profileData.credits || 0
            api_key.value = profileData.api_key
          }
        } else {
          credits.value = 0
          api_key.value = null
        }
      })
    } catch (error) {
      console.error('Error initializing auth:', error)
    } finally {
      loading.value = false
    }
  }

  async function loadUserWithProfile() {
    try {
      loading.value = true
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      user.value = currentUser

      if (currentUser) {
        uid.value = currentUser.id

        // Try to get existing profile first
        let { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('credits, api_key')
          .eq('user_id', currentUser.id)
          .single()

        // If no profile exists, create one
        if (profileError || !profileData) {
          const { data: newProfile, error: insertError } = await supabase
            .from('profiles')
            .insert([
              { 
                user_id: currentUser.id,
                credits: 3,
                api_key: crypto.randomUUID()
              }
            ])
            .select()
            .single()

          if (!insertError && newProfile) {
            profileData = newProfile
          }
        }

        if (profileData) {
          credits.value = profileData.credits || 0
          api_key.value = profileData.api_key
        }
      }
    } catch (error) {
      console.error('Error loading user:', error)
      user.value = null
    } finally {
      loading.value = false
    }
  }

  async function signIn({ email, password, options }) {
    try {
      loading.value = true
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
        options
      })
      if (error) throw getTranslatedError(error)
      console.log('signIn successful, data:', data) // Log successful data
      user.value = data.user
      session.value = data.session
      uid.value = data.user.id
      authAction.value = 'login'

      // Fetch profile data after successful sign in
      if (data.user) {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('credits, api_key')
          .eq('user_id', data.user.id)
          .single()

        if (!profileError && profileData) {
          credits.value = profileData.credits || 0
          api_key.value = profileData.api_key
        }
      }

      return { data, error: null }
    } catch (error) {
      console.error('Sign in error:', error)
      throw error // Re-throw the error to be caught in Login.vue
    } finally {
      loading.value = false
    }
  }

  async function signUp({ email, password, options }) {
    try {
      loading.value = true
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options
      })
      if (error) throw getTranslatedError(error)
      // Profile with 3 credits and api_key is automatically created by database trigger
      return { data, error: null }
    } catch (error) {
      console.error('Sign up error:', error)
      return { data: null, error }
    } finally {
      loading.value = false
    }
  }

  async function signOut() {
    try {
      loading.value = true
      // Force clear local state first
      user.value = null
      session.value = null
      credits.value = 0
      api_key.value = null
      uid.value = null
      
      // Then send signout request to Supabase
      const { data, error } = await supabase.auth.signOut()
      console.log('Supabase signOut result:', { data, error }) // Log the result
      if (error) throw error

      // Force clear browser cache
      authAction.value = 'logout'
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  async function testSupabaseConnection() {
    try {
      // Simple ping to Supabase auth endpoint
      const { error } = await supabase.auth.getUser()
      
      // If we get any response (even unauthorized), connection is OK
      return { status: 'ok' }
    } catch (error) {
      console.error('Supabase connection error:', error)
      return { 
        status: 'error',
        message: 'Impossible de se connecter au serveur'
      }
    }
  }

  async function signInWithGoogle() {
    try {
      loading.value = true
      const { data: { session: currentSession }, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/restricted`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent'
          }
        }
      })
      if (error) throw error

      // Set auth state as soon as we have session data
      if (currentSession?.user) {
        session.value = currentSession
        user.value = currentSession.user
        uid.value = currentSession.user.id
        authAction.value = 'login'

        // Also try to get/create profile for the user
        let { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('credits, api_key')
          .eq('user_id', currentSession.user.id)
          .single()

        if (profileError || !profileData) {
          const { data: newProfile, error: insertError } = await supabase
            .from('profiles')
            .insert([
              { 
                user_id: currentSession.user.id,
                credits: 3,
                api_key: crypto.randomUUID()
              }
            ])
            .select()
            .single()

          if (!insertError) {
            profileData = newProfile
          }
        }

        if (profileData) {
          credits.value = profileData.credits || 0
          api_key.value = profileData.api_key
        }
      }

      return { data: currentSession, error: null }
    } catch (error) {
      console.error('Google sign in error:', error)
      return { data: null, error }
    } finally {
      loading.value = false
    }
  }

  // Handle auth state after redirect
  async function handleAuthStateChange(event, newSession) {
    session.value = newSession
    user.value = newSession?.user ?? null
    uid.value = newSession?.user?.id ?? null

    if (event === 'SIGNED_IN') {
      authAction.value = 'login'
      await loadUserWithProfile()
    } else if (event === 'SIGNED_OUT') {
      authAction.value = 'logout'
      user.value = null
      session.value = null
      credits.value = 0
      api_key.value = null
      uid.value = null
    }
  }

  // Subscribe to auth changes
  supabase.auth.onAuthStateChange(handleAuthStateChange)

  async function fetchProfile() {
    if (!user.value) return;
    try {
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('credits, api_key')
        .eq('user_id', user.value.id)
        .single();

      if (!profileError && profileData) {
        credits.value = profileData.credits || 0;
        api_key.value = profileData.api_key;
      } else {
        console.error('Error fetching profile:', profileError);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  }

  return {
    user,
    loading,
    isAuthenticated,
    session,
    initialize,
    loadUserWithProfile,
    signIn,
    signUp,
    signOut,
    testSupabaseConnection,
    credits,
    api_key,
    authAction,
    uid,
    signInWithGoogle,
    questionText, // Expose the new state property
    setQuestionText: (text) => { questionText.value = text }, // New action to set question text
    fetchProfile // Expose the new function
  }

  function getTranslatedError(error) {
    const errorMap = {
      'Invalid login credentials': 'Identifiants invalides. Veuillez réessayer.',
      'Email not confirmed': 'Email non confirmé. Veuillez vérifier votre boîte de réception.',
      'User already registered': 'Utilisateur déjà enregistré.',
      'Weak password': 'Le mot de passe doit contenir au moins 6 caractères',
      'Email rate limit exceeded': 'Trop de tentatives. Veuillez réessayer plus tard.',
      'Password recovery requires an email': 'Une adresse email est requise pour la récupération du mot de passe'
    }
    return new Error(errorMap[error.message] || error.message)
  }
})
