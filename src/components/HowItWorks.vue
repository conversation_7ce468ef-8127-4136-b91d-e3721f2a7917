<template>
  <!-- About/How It Works Section -->
  <section id="comment" class="py-12 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden" data-aos="fade-up">
    <div class="max-w-7xl mx-auto px-8 md:px-12 py-3 relative z-10">
      <!-- Section Header -->
      <div class="text-center mb-8" data-aos="fade-down" data-aos-duration="800">
        <h2 class="text-3xl font-bold text-gray-800 mb-2">Comment ça marche ?</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Notre plateforme vous offre un moyen simple et efficace d'obtenir des réponses à vos questions juridiques en seulement trois étapes.
        </p>
      </div>

      <!-- Linear Process Visualization -->
      <div
        ref="linearProcess"
        class="w-full h-[200px] mb-8 bg-white rounded-xl"
        data-aos="fade-up"
        data-aos-delay="150"
        data-aos-duration="1000"
      ></div>

      <!-- Call to Action -->
      <div class="text-center mt-6" data-aos="zoom-in" data-aos-delay="500" data-aos-duration="800">
        <a
          href="#analyse"
          class="inline-flex items-center px-8 py-3 bg-blue-700 text-white font-medium rounded-full hover:bg-blue-800 transition-colors duration-300 shadow-md"
        >
          <span>Essayer maintenant</span>
          <i class="fas fa-arrow-right ml-2"></i>
        </a>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import * as d3 from 'd3'

const linearProcess = ref(null)
let resizeHandler = null

onMounted(() => {
  createVisualization()
  
  // Add resize listener
  resizeHandler = () => handleResize()
  window.addEventListener('resize', resizeHandler)
})

onUnmounted(() => {
  if (resizeHandler) {
    window.removeEventListener('resize', resizeHandler)
  }
})

const createVisualization = () => {
  if (!linearProcess.value) return

  // Set up the margin, width, and height for our SVG
  const margin = { top: 40, right: 40, bottom: 40, left: 40 }
  let width = linearProcess.value.clientWidth - margin.left - margin.right
  let height = linearProcess.value.clientHeight - margin.top - margin.bottom

  // Clear any existing SVG
  d3.select(linearProcess.value).selectAll("*").remove()

  // Create the SVG element
  const svg = d3.select(linearProcess.value)
    .append("svg")
    .attr("width", width + margin.left + margin.right)
    .attr("height", height + margin.top + margin.bottom)
    .append("g")
    .attr("transform", `translate(${margin.left}, ${margin.top})`)

  // Calculate positions based on available width
  const iconSpacing = width / 4
  const icons = [
    { id: 1, x: iconSpacing, y: height / 2, icon: "Inscription", iconType: "fa-user-plus" },
    { id: 2, x: iconSpacing * 2, y: height / 2, icon: "Poser votre question", iconType: "fa-question-circle" },
    { id: 3, x: iconSpacing * 3, y: height / 2, icon: "Visualiser la réponse", iconType: "fa-file-alt" }
  ]

  const arrows = icons.slice(0, -1).map((icon, index) => ({
    id: index + 1,
    x1: icon.x + 25,
    y1: icon.y,
    x2: icons[index + 1].x - 25,
    y2: icons[index + 1].y
  }))

  // Add the dotted lines (arrows)
  svg.selectAll("path")
    .data(arrows)
    .enter()
    .append("path")
    .attr("d", d => `M${d.x1},${d.y1} L${d.x2},${d.y2}`)
    .attr("stroke", "#6B7280")
    .attr("stroke-width", 2)
    .attr("stroke-dasharray", "5,5")
    .attr("fill", "none")

  // Add arrowheads
  svg.selectAll("polygon")
    .data(arrows)
    .enter()
    .append("polygon")
    .attr("points", d => `${d.x2 - 10},${d.y2 - 5} ${d.x2},${d.y2} ${d.x2 - 10},${d.y2 + 5}`)
    .attr("fill", "#6B7280")

  // Add the circles (icons)
  const circles = svg.selectAll(".icon-circle")
    .data(icons)
    .enter()
    .append("g")
    .attr("class", "icon-circle")
    .attr("transform", d => `translate(${d.x},${d.y})`)

  circles.append("circle")
    .attr("r", 20)
    .attr("fill", (d, i) => i % 2 === 0 ? "#ce2029" : "#1d4ed8") // Alternate red and blue
    .attr("cursor", "pointer")
    .on("mouseover", function (event, d) {
      const color = d.id % 2 === 0 ? "#1e40af" : "#dc2626"
      d3.select(this)
        .transition()
        .duration(200)
        .attr("r", 22)
        .attr("fill", color)
    })
    .on("mouseout", function (event, d) {
      const color = d.id % 2 === 0 ? "#1d4ed8" : "#ce2029"
      d3.select(this)
        .transition()
        .duration(200)
        .attr("r", 20)
        .attr("fill", color)
    })

  // Add labels below circles
  circles.append("text")
    .attr("y", 35)
    .attr("text-anchor", "middle")
    .attr("fill", "#374151")
    .style("font-family", "system-ui, -apple-system, sans-serif")
    .style("font-size", "14px")
    .style("font-weight", "500")
    .text(d => d.icon)

  // Add step numbers inside circles
  circles.append("text")
    .attr("text-anchor", "middle")
    .attr("dy", "0.35em")
    .attr("fill", "white")
    .style("font-family", "system-ui, -apple-system, sans-serif")
    .style("font-size", "16px")
    .style("font-weight", "bold")
    .text(d => d.id)
}

const handleResize = () => {
  createVisualization()
}
</script>

<style scoped>
/* Additional styles if needed */
</style>
