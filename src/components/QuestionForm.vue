<template>
  <!-- Search Section -->
  <section id="analyse" class="py-16 bg-gradient-to-b from-gray-100 to-white relative overflow-hidden" data-aos="fade-up">
    <div class="max-w-12xl mx-auto px-8 md:px-14 relative z-10">
      <!-- Section Header -->
      <div class="text-center mb-10" data-aos="fade-down" data-aos-duration="800">
        <h2 class="text-3xl font-bold text-gray-800 mb-2">Posez votre question juridique ou legale</h2>
        <p class="text-gray-600 max-w-2xl mx-auto">
          Obtenez une réponse immédiate et personnalisée à votre question juridique
        </p>
      </div>

      <div class="flex flex-col items-center gap-4">
        <!-- Search Box -->
        <div
          class="w-full max-w-6xl bg-white shadow-xl rounded-xl p-6 md:p-8 text-left"
          data-aos="zoom-in"
          data-aos-duration="1000"
          data-aos-delay="200"
        >
          <h3 class="text-xl font-semibold mb-4 text-gray-800 flex items-center">
            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center mr-3">
              <i class="fas fa-question text-red-600"></i>
            </div>
            Posez votre question (réponse immédiate)
          </h3>

          <div class="w-full mt-6">
            <!-- Animated Gradient Border Textarea with Word Limit -->
            <div class="gradient-border-outer w-full max-w-none" :class="{ zoomed: isTextareaFocused }" ref="borderWrapper">
              <div class="gradient-border-inner w-full">
                <textarea
                  ref="questionTextarea"
                  v-model="formData"
                  class="custom-textarea"
                  placeholder="Saisissez votre question juridique ici... Par exemple: 'Comment contester une amende?' ou 'Quels sont mes droits en cas de licenciement?'"
                  aria-label="Saisie de question juridique"
                  @focus="handleTextareaFocus"
                  @blur="handleTextareaBlur"
                  @input="updateWordCount"
                  @keydown="handleKeydown"
                  @click="handleTextareaClick"
                ></textarea>
                <div class="textarea-buttons" role="group" aria-label="Actions de la zone de texte">
                  <button
                    type="button"
                    class="bg-blue-500 hover:bg-blue-600 w-8 h-8 flex items-center justify-center rounded-full text-white transition"
                    title="Pièce jointe"
                    @click="handleAttachment"
                    aria-label="Pièce jointe"
                  >
                    <i class="fa fa-paperclip" aria-hidden="true"></i>
                  </button>
                  <button
                    type="button"
                    class="bg-red-600 hover:bg-red-700 w-32 h-8 flex items-center justify-center rounded-full text-white transition space-x-2 shadow-md"
                    title="Rechercher"
                    @click="handleSubmit"
                    aria-label="Rechercher"
                  >
                    <i class="fa fa-search"></i>
                    <span>Analyse</span>
                  </button>
                </div>
                <div class="word-count">Mots: {{ wordCount }}/{{ WORD_LIMIT }}</div>
                <div v-show="showWordLimitMessage" class="info-message">
                  Limite de mots atteinte ({{ WORD_LIMIT }} mots maximum).
                </div>
              </div>
            </div>
          </div>

          <div class="flex flex-col md:flex-row justify-between items-start md:items-center w-full mt-6">
            <div class="text-red-600 font-medium flex items-center mb-4 md:mb-0">
              <i class="fas fa-shield-alt mr-2"></i>
              <span>Vos données sont protégées et ne sont pas stockées</span>
            </div>
          </div>

          <!-- French detection message -->
          <div v-if="frenchDetectionMessage" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {{ frenchDetectionMessage }}
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useQuestionStore } from '../stores/question'

const router = useRouter()
const authStore = useAuthStore()
const questionStore = useQuestionStore()

const formData = ref('')
const isTextareaFocused = ref(false)
const showWordLimitMessage = ref(false)
const frenchDetectionMessage = ref('')
const WORD_LIMIT = 500

const questionTextarea = ref(null)
const borderWrapper = ref(null)

// Computed property for word count
const wordCount = computed(() => {
  const words = getWords(formData.value)
  return words[0] ? words.length : 0
})

// Function to get words from text
const getWords = (text) => {
  return text.trim().split(/\s+/).filter(Boolean)
}

// Function to detect French language
const detectFrench = (text) => {
  const frenchWords = [
    'le', 'la', 'les', 'un', 'une', 'des',
    'je', 'tu', 'il', 'elle', 'nous', 'vous',
    'de', 'à', 'en', 'dans', 'sur', 'avec',
    'et', 'mais', 'ou', 'car',
    'être', 'avoir', 'faire', 'dire', 'aller'
  ]

  const frenchGrammarIndicators = [
    'é', 'è', 'ê', 'ë', 'à', 'â', 'î', 'ï', 'ô', 'ù', 'û', 'ç',
    'ais', 'ait', 'ions', 'iez', 'aient',
    '-t-il', '-t-elle', 'qu\'', 'n\'', 'm\'', 't\'', 's\'', 'c\''
  ]

  const lowerText = text.toLowerCase()

  const frenchScore = {
    commonWords: 0,
    accents: 0,
    grammarPatterns: 0
  }

  frenchWords.forEach(word => {
    if (lowerText.includes(word)) {
      frenchScore.commonWords += 1
    }
  })

  const accentCount = (lowerText.match(/[éèêëàâîïôùûç]/g) || []).length
  frenchScore.accents = accentCount

  frenchGrammarIndicators.forEach(pattern => {
    if (lowerText.includes(pattern)) {
      frenchScore.grammarPatterns += 1
    }
  })

  const totalScore =
    frenchScore.commonWords * 2 +
    frenchScore.accents * 1.5 +
    frenchScore.grammarPatterns * 3

  return {
    isLikelyFrench: totalScore > 5,
    score: totalScore,
    details: frenchScore
  }
}

// Handle textarea focus
const handleTextareaFocus = () => {
  isTextareaFocused.value = true
}

// Handle textarea blur
const handleTextareaBlur = () => {
  isTextareaFocused.value = false
  setTimeout(() => {
    showWordLimitMessage.value = false
  }, 300)
}

// Handle textarea click - redirect to login if not authenticated
const handleTextareaClick = () => {
  if (!authStore.isAuthenticated) {
    router.push({
      path: '/login',
      query: {
        redirect: '/'
      }
    })
  }
}

// Update word count and handle limit
const updateWordCount = () => {
  const words = getWords(formData.value)
  
  if (words.length > WORD_LIMIT) {
    formData.value = words.slice(0, WORD_LIMIT).join(' ')
  }
  
  if (words.length >= WORD_LIMIT) {
    showWordLimitMessage.value = true
  } else {
    showWordLimitMessage.value = false
  }
}

// Handle keydown for word limit
const handleKeydown = (e) => {
  const allowedKeys = [
    'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Tab', 'Home', 'End', 'Control', 'Meta', 'Alt'
  ]
  const words = getWords(formData.value)

  // If at limit, block input unless it's a control key or editing
  if (
    words.length >= WORD_LIMIT &&
    !allowedKeys.includes(e.key) &&
    // Allow selection replacement
    !(questionTextarea.value.selectionStart !== questionTextarea.value.selectionEnd)
  ) {
    // Prevent input (except navigation/editing keys)
    e.preventDefault()
    showWordLimitMessage.value = true
  }
}

// Handle attachment button
const handleAttachment = () => {
  alert('Pièce jointe cliquée')
}

// Handle form submission
const handleSubmit = () => {
  if (!authStore.isAuthenticated) {
    router.push({
      path: '/login',
      query: {
        redirect: '/app/reponse-legale'
      }
    })
    return
  }

  // Check if the input text is French
  const detectionResult = detectFrench(formData.value)
  if (!detectionResult.isLikelyFrench) {
    frenchDetectionMessage.value = 'Le texte saisi ne semble pas être en français. Veuillez saisir votre question en français.'
    return
  } else {
    frenchDetectionMessage.value = '' // Clear message if it was previously set
  }

  // Store the question text in the question store
  questionStore.setQuestion(formData.value)

  // Navigate to the response page
  router.push({
    name: 'ReponseLegale'
  })
}
</script>

<style scoped>
.gradient-border-outer {
  width: 100%;
  max-width: none;
  border-radius: 0.75rem;
  padding: 3px;
  background: linear-gradient(270deg, #ef4444, #3b82f6, #8b5cf6, #ef4444);
  background-size: 400% 400%;
  animation: gradient-animate 2s linear infinite;
  position: relative;
  box-sizing: border-box;
  transition: transform 0.25s cubic-bezier(.4, 2, .6, 1), box-shadow 0.25s, background 0.25s;
}

.gradient-border-outer.zoomed {
  transform: scale(1.03);
  box-shadow: 0 0 0 8px rgba(220, 38, 38, 0.10);
  background: transparent !important;
  animation: none !important;
  border: 3px solid #dc2626;
  padding: 0;
}

.gradient-border-inner {
  background: #fff;
  border-radius: 0.75rem;
  width: 100%;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  min-height: 300px;
}

.custom-textarea {
  width: 100%;
  padding: 1.5rem 1rem 4.5rem 1rem;
  border-radius: 0.75rem;
  border: none !important;
  outline: none !important;
  background: transparent;
  color: #374151;
  resize: vertical;
  font-size: 1.25rem;
  box-sizing: border-box;
  display: block;
  z-index: 2;
  position: relative;
}

.custom-textarea:focus {
  border: none !important;
  outline: none !important;
}

.textarea-buttons {
  position: absolute;
  right: 1.5rem;
  bottom: 1.5rem;
  display: flex;
  gap: 0.5rem;
  z-index: 3;
  pointer-events: auto;
}

.word-count {
  position: absolute;
  left: 1.5rem;
  bottom: 1.5rem;
  font-size: 1.1rem;
  color: #666;
  z-index: 3;
  background: rgba(255, 255, 255, 0.85);
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  pointer-events: none;
  user-select: none;
}

.info-message {
  position: absolute;
  left: 50%;
  bottom: 5rem;
  transform: translateX(-50%);
  background: #fee2e2;
  color: #b91c1c;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.10);
  pointer-events: none;
  user-select: none;
}

@keyframes gradient-animate {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}
</style>
