import { createRouter, createWebHistory } from 'vue-router'
import DefaultLayout from '../layouts/DefaultLayout.vue'
import AuthLayout from '../layouts/AuthLayout.vue'
import { requireAuth, allowPublic } from '../middleware/auth'
import Home from '../views/Home.vue'
import Login from '../views/Login.vue'
import Register from '../views/Register.vue'
import Logout from '../views/Logout.vue'
import ResetPassword from '../views/ResetPassword.vue'
import UpdatePassword from '../views/UpdatePassword.vue'

// Lazy load restricted views to avoid loading Supabase unnecessarily
const RestrictedView = () => import('../views/restricted/RestrictedView.vue')
const Question = () => import('../views/restricted/Question.vue')
const ReponseLegale = () => import('../views/restricted/ReponseLegale.vue')

const routes = [
  {
    path: '/',
    component: DefaultLayout,
    children: [
      {
        path: '',
        name: 'Home',
        component: Home,
        beforeEnter: [allowPublic],
        meta: {
          title: 'QuestionLegale.info: Trouvez des réponses juridiques en France',
          description: 'QuestionLegale.info repond a vos questions d\'ordre legale ou juridique et vous oriente vers le professinnel idoine pour votre situation.',
          keywords: 'justice France, démarches juridiques, tribunal, droits, procédures judiciaires, QuestionLegale, Reponses Juridiques, Avocats, Huissiers, Notaires, Questions Legales',
          requiresAuth: false
        }
      },
      {
        path: 'logout',
        name: 'Logout',
        component: Logout,
        meta: {
          title: 'Déconnexion',
          requiresAuth: false
        }
      },
      {
        path: 'update-password',
        name: 'UpdatePassword',
        component: UpdatePassword,
        meta: {
          title: 'Mettre à jour le mot de passe',
          requiresAuth: false
        }
      },
      // Restricted routes group
      {
        path: 'app',
        meta: { requiresAuth: true },
        beforeEnter: [requireAuth],
        children: [
          {
            path: 'restricted',
            name: 'RestrictedView',
            component: RestrictedView,
            meta: { 
              title: 'Zone restreinte'
            }
          },
          {
            path: 'question',
            name: 'Question',
            component: Question,
            meta: {
              title: 'Poser une question'
            }
          },
          {
            path: 'reponse-legale',
            name: 'ReponseLegale',
            component: ReponseLegale,
            meta: {
              title: 'Réponse Légale'
            }
          },
        ]
      },
    ]
  },
  {
    path: '/',
    component: AuthLayout,
    children: [
      {
        path: 'login',
        name: 'Login',
        component: Login,
        meta: {
          title: 'Connexion',
          requiresAuth: false
        }
      },
      {
        path: 'register',
        name: 'Register',
        component: Register,
        meta: {
          title: "S'inscrire",
          requiresAuth: false
        }
      },
      {
        path: 'reset-password',
        name: 'ResetPassword',
        component: ResetPassword,
        meta: {
          title: 'Réinitialiser le mot de passe',
          requiresAuth: false
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Global navigation guard
router.beforeEach(async (to, from, next) => {
  // Update page title
  if (to.meta.title) {
    document.title = to.meta.title
  } else {
    document.title = 'QuestionLegale.info'
  }

  // Update meta description
  if (to.meta.description) {
    let metaDescription = document.querySelector('meta[name="description"]')
    if (!metaDescription) {
      metaDescription = document.createElement('meta')
      metaDescription.name = 'description'
      document.head.appendChild(metaDescription)
    }
    metaDescription.content = to.meta.description
  }

  // Update meta keywords
  if (to.meta.keywords) {
    let metaKeywords = document.querySelector('meta[name="keywords"]')
    if (!metaKeywords) {
      metaKeywords = document.createElement('meta')
      metaKeywords.name = 'keywords'
      document.head.appendChild(metaKeywords)
    }
    metaKeywords.content = to.meta.keywords
  }

  // Check if route requires auth
  if (to.matched.some(record => record.meta.requiresAuth)) {
    const authStore = await import('../stores/auth').then(m => m.useAuthStore())
    if (!authStore.isAuthenticated) {
      return next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    }
  }

  next()
})

export default router
