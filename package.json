{"name": "supavite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.5.1", "@supabase/supabase-js": "^2.49.4", "aos": "^2.3.1", "axios": "^1.9.0", "d3": "^7.8.5", "markdown-it": "^14.1.0", "marked": "^15.0.11", "pinia": "^2.3.1", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@types/estree": "^1.0.7", "@types/node": "^22.15.24", "@types/ws": "^8.18.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/language-server": "^2.2.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.10"}}